#!/usr/bin/env python3
"""
Test Mistral OCR API directly
"""

import os
import base64
from mistralai import Mistral
from PIL import Image, ImageDraw, ImageFont
import io

def create_test_image():
    """Create a simple test image with text."""
    # Create a white image
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Add some text
    try:
        # Try to use a default font
        font = ImageFont.load_default()
    except:
        font = None
    
    text = """Student Name: John <PERSON>
Student ID: 123456789
GPA: 3.85
Major: Computer Science
Graduation Date: May 2024"""
    
    draw.text((20, 20), text, fill='black', font=font)
    
    # Save to bytes
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    
    return img_byte_arr.getvalue()

def test_mistral_ocr_api():
    """Test Mistral OCR API directly."""
    try:
        # Create test image
        print("Creating test image...")
        image_data = create_test_image()
        
        # Initialize Mistral client
        api_key = "vKLtdsfpBsXtL1tKvXBsILEEZk6O0Xty"
        client = Mistral(api_key=api_key)
        
        # Convert to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        document_url = f"data:image/png;base64,{image_base64}"
        
        print("Calling Mistral OCR API...")
        
        # Call OCR API for image
        ocr_response = client.ocr.process(
            model="mistral-ocr-latest",
            document={
                "type": "image_url",
                "image_url": document_url
            },
            include_image_base64=True
        )
        
        print("✅ Mistral OCR API call successful!")
        print(f"Response type: {type(ocr_response)}")
        print(f"Response attributes: {dir(ocr_response)}")
        
        # Try to extract text
        if hasattr(ocr_response, 'text'):
            print(f"Text: {ocr_response.text}")
        if hasattr(ocr_response, 'content'):
            print(f"Content: {ocr_response.content}")
        if hasattr(ocr_response, 'result'):
            print(f"Result: {ocr_response.result}")
            
        # Print all non-private attributes
        for attr in dir(ocr_response):
            if not attr.startswith('_'):
                try:
                    value = getattr(ocr_response, attr)
                    if not callable(value):
                        print(f"{attr}: {value}")
                except:
                    pass
        
    except Exception as e:
        print(f"❌ Error testing Mistral OCR API: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mistral_ocr_api()
