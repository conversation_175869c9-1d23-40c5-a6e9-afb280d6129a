"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, QueryParamMetadata
from typing import Optional
from typing_extensions import Annotated, NotRequired, TypedDict


class FilesAPIRoutesGetSignedURLRequestTypedDict(TypedDict):
    file_id: str
    expiry: NotRequired[int]
    r"""Number of hours before the url becomes invalid. Defaults to 24h"""


class FilesAPIRoutesGetSignedURLRequest(BaseModel):
    file_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    expiry: Annotated[
        Optional[int],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = 24
    r"""Number of hours before the url becomes invalid. Defaults to 24h"""
