"""Code generated by Speakeasy (https://speakeasyapi.dev). DO NOT EDIT."""

from typing import Any, Callable, Dict, Optional, Union

import httpx
from mistralai_azure import models, utils
from mistralai_azure._hooks import SDKHooks
from mistralai_azure.chat import Chat
from mistralai_azure.types import Nullable

from .basesdk import BaseSDK
from .httpclient import AsyncHttpClient, HttpClient
from .sdkconfiguration import SDKConfiguration
from .utils.logger import Logger, NoOpLogger
from .utils.retries import RetryConfig


class MistralAzure(BaseSDK):
    r"""Mistral AI API: Our Chat Completion and Embeddings APIs specification. Create your account on [La Plateforme](https://console.mistral.ai) to get access and read the [docs](https://docs.mistral.ai) to learn how to use it."""

    chat: Chat
    r"""Chat Completion API"""

    def __init__(
        self,
        azure_api_key: Union[str, Callable[[], str]],
        azure_endpoint: str,
        url_params: Optional[Dict[str, str]] = None,
        client: Optional[HttpClient] = None,
        async_client: Optional[AsyncHttpClient] = None,
        retry_config: Optional[Nullable[RetryConfig]] = None,
        debug_logger: Optional[Logger] = None,
    ) -> None:
        r"""Instantiates the SDK configuring it with the provided parameters.

        :param azure_api_key: The azure_api_key required for authentication
        :param azure_endpoint: The Azure AI endpoint URL to use for all methods
        :param url_params: Parameters to optionally template the server URL with
        :param client: The HTTP client to use for all synchronous methods
        :param async_client: The Async HTTP client to use for all asynchronous methods
        :param retry_config: The retry configuration to use for all supported methods
        """
        # if azure_endpoint doesn't end with `/v1` add it
        if not azure_endpoint.endswith("/"):
            azure_endpoint += "/"
        if not azure_endpoint.endswith("v1/"):
            azure_endpoint += "v1/"
        server_url = azure_endpoint

        if client is None:
            client = httpx.Client()

        assert issubclass(
            type(client), HttpClient
        ), "The provided client must implement the HttpClient protocol."

        if async_client is None:
            async_client = httpx.AsyncClient()

        assert issubclass(
            type(async_client), AsyncHttpClient
        ), "The provided async_client must implement the AsyncHttpClient protocol."

        if debug_logger is None:
            debug_logger = NoOpLogger()

        security: Any = None
        if callable(azure_api_key):
            security = lambda: models.Security(  # pylint: disable=unnecessary-lambda-assignment
                api_key=azure_api_key()
            )
        else:
            security = models.Security(api_key=azure_api_key)

        if server_url is not None:
            if url_params is not None:
                server_url = utils.template_url(server_url, url_params)

        BaseSDK.__init__(
            self,
            SDKConfiguration(
                client=client,
                async_client=async_client,
                security=security,
                server_url=server_url,
                server=None,
                retry_config=retry_config,
                debug_logger=debug_logger,
            ),
        )

        hooks = SDKHooks()

        current_server_url, *_ = self.sdk_configuration.get_server_details()
        server_url, self.sdk_configuration.client = hooks.sdk_init(
            current_server_url, self.sdk_configuration.client
        )
        if current_server_url != server_url:
            self.sdk_configuration.server_url = server_url

        # pylint: disable=protected-access
        self.sdk_configuration.__dict__["_hooks"] = hooks

        self._init_sdks()

    def _init_sdks(self):
        self.chat = Chat(self.sdk_configuration)
