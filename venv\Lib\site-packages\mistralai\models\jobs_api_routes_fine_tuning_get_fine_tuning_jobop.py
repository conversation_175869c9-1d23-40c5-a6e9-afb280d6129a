"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .classifierdetailedjobout import (
    ClassifierDetailedJobOut,
    ClassifierDetailedJobOutTypedDict,
)
from .completiondetailedjobout import (
    CompletionDetailedJobOut,
    CompletionDetailedJobOutTypedDict,
)
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, get_discriminator
from pydantic import Discriminator, Tag
from typing import Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


class JobsAPIRoutesFineTuningGetFineTuningJobRequestTypedDict(TypedDict):
    job_id: str
    r"""The ID of the job to analyse."""


class JobsAPIRoutesFineTuningGetFineTuningJobRequest(BaseModel):
    job_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]
    r"""The ID of the job to analyse."""


JobsAPIRoutesFineTuningGetFineTuningJobResponseTypedDict = TypeAliasType(
    "JobsAPIRoutesFineTuningGetFineTuningJobResponseTypedDict",
    Union[CompletionDetailedJobOutTypedDict, ClassifierDetailedJobOutTypedDict],
)
r"""OK"""


JobsAPIRoutesFineTuningGetFineTuningJobResponse = Annotated[
    Union[
        Annotated[ClassifierDetailedJobOut, Tag("classifier")],
        Annotated[CompletionDetailedJobOut, Tag("completion")],
    ],
    Discriminator(lambda m: get_discriminator(m, "job_type", "job_type")),
]
r"""OK"""
