"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .inputs import Inputs, InputsTypedDict
from mistralai.types import BaseModel
import pydantic
from typing_extensions import Annotated, TypedDict


class ChatClassificationRequestTypedDict(TypedDict):
    model: str
    inputs: InputsTypedDict
    r"""Chat to classify"""


class ChatClassificationRequest(BaseModel):
    model: str

    inputs: Annotated[Inputs, pydantic.Field(alias="input")]
    r"""Chat to classify"""
