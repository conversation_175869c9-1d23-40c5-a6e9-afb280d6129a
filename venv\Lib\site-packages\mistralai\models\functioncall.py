"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import Any, Dict, Union
from typing_extensions import TypeAliasType, TypedDict


ArgumentsTypedDict = TypeAliasType("ArgumentsTypedDict", Union[Dict[str, Any], str])


Arguments = TypeAliasType("Arguments", Union[Dict[str, Any], str])


class FunctionCallTypedDict(TypedDict):
    name: str
    arguments: ArgumentsTypedDict


class FunctionCall(BaseModel):
    name: str

    arguments: Arguments
