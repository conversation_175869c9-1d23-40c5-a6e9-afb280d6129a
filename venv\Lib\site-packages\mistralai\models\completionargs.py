"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .completionargsstop import CompletionArgsStop, CompletionArgsStopTypedDict
from .prediction import Prediction, PredictionTypedDict
from .responseformat import ResponseFormat, ResponseFormatTypedDict
from .toolchoiceenum import ToolChoiceEnum
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import Optional
from typing_extensions import NotRequired, TypedDict


class CompletionArgsTypedDict(TypedDict):
    r"""White-listed arguments from the completion API"""

    stop: NotRequired[Nullable[CompletionArgsStopTypedDict]]
    presence_penalty: NotRequired[Nullable[float]]
    frequency_penalty: NotRequired[Nullable[float]]
    temperature: NotRequired[float]
    top_p: NotRequired[Nullable[float]]
    max_tokens: NotRequired[Nullable[int]]
    random_seed: NotRequired[Nullable[int]]
    prediction: NotRequired[Nullable[PredictionTypedDict]]
    response_format: NotRequired[Nullable[ResponseFormatTypedDict]]
    tool_choice: NotRequired[ToolChoiceEnum]


class CompletionArgs(BaseModel):
    r"""White-listed arguments from the completion API"""

    stop: OptionalNullable[CompletionArgsStop] = UNSET

    presence_penalty: OptionalNullable[float] = UNSET

    frequency_penalty: OptionalNullable[float] = UNSET

    temperature: Optional[float] = 0.3

    top_p: OptionalNullable[float] = UNSET

    max_tokens: OptionalNullable[int] = UNSET

    random_seed: OptionalNullable[int] = UNSET

    prediction: OptionalNullable[Prediction] = UNSET

    response_format: OptionalNullable[ResponseFormat] = UNSET

    tool_choice: Optional[ToolChoiceEnum] = None

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "stop",
            "presence_penalty",
            "frequency_penalty",
            "temperature",
            "top_p",
            "max_tokens",
            "random_seed",
            "prediction",
            "response_format",
            "tool_choice",
        ]
        nullable_fields = [
            "stop",
            "presence_penalty",
            "frequency_penalty",
            "top_p",
            "max_tokens",
            "random_seed",
            "prediction",
            "response_format",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
