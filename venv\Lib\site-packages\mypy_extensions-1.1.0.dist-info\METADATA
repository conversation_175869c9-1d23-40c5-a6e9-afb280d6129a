Metadata-Version: 2.4
Name: mypy_extensions
Version: 1.1.0
Summary: Type system extensions for programs checked with the mypy type checker.
Author-email: The mypy developers <<EMAIL>>
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-Expression: MIT
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Software Development
License-File: LICENSE
Project-URL: Homepage, https://github.com/python/mypy_extensions

Mypy Extensions
===============

The `mypy_extensions` module defines extensions to the Python standard
library `typing` module that are supported by the mypy type checker and
the mypyc compiler.

