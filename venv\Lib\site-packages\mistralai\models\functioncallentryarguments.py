"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Any, Dict, Union
from typing_extensions import TypeAliasType


FunctionCallEntryArgumentsTypedDict = TypeAliasType(
    "FunctionCallEntryArgumentsTypedDict", Union[Dict[str, Any], str]
)


FunctionCallEntryArguments = TypeAliasType(
    "FunctionCallEntryArguments", Union[Dict[str, Any], str]
)
