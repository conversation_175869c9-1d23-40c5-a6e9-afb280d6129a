"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


ArchiveFTModelOutObject = Literal["model"]


class ArchiveFTModelOutTypedDict(TypedDict):
    id: str
    object: NotRequired[ArchiveFTModelOutObject]
    archived: NotRequired[bool]


class ArchiveFTModelOut(BaseModel):
    id: str

    object: Optional[ArchiveFTModelOutObject] = "model"

    archived: Optional[bool] = True
