import io
import base64
import logging
import tempfile
import os
from typing import Union, List
from PIL import Image
import PyPDF2
from pdf2image import convert_from_bytes
from mistralai import Mistral, UserMessage
from app.config import settings


logger = logging.getLogger(__name__)


class OCRService:
    def __init__(self):
        """Initialize the OCR service with Mistral client."""
        try:
            # Initialize Mistral client for OCR
            self.client = Mistral(api_key=settings.mistral_api_key)
            logger.info("Mistral OCR service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Mistral OCR service: {e}")
            raise

    async def extract_text_from_file(self, file_content: bytes, filename: str) -> str:
        """
        Extract text from PDF or image file and return as markdown using Mistral OCR.

        Args:
            file_content: The file content as bytes
            filename: The original filename

        Returns:
            Extracted text formatted as markdown
        """
        try:
            file_extension = filename.lower().split('.')[-1]

            if file_extension == 'pdf':
                return await self._extract_from_pdf(file_content)
            elif file_extension in ['png', 'jpg', 'jpeg', 'tiff', 'bmp']:
                return await self._extract_from_image(file_content)
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")

        except Exception as e:
            logger.error(f"Error extracting text from {filename}: {e}")
            raise

    async def _extract_from_pdf(self, pdf_content: bytes) -> str:
        """Extract text from PDF file using Mistral OCR API."""
        try:
            logger.info(f"Starting PDF processing with Mistral OCR, size: {len(pdf_content)} bytes")

            # Save PDF to temporary file for Mistral OCR API
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(pdf_content)
                temp_file_path = temp_file.name

            try:
                # Use Mistral OCR API directly
                extracted_text = await self._extract_with_mistral_ocr_api(temp_file_path, "pdf")
                logger.info(f"Mistral OCR extracted text length: {len(extracted_text) if extracted_text else 0}")

                return extracted_text if extracted_text.strip() else "No text found in PDF"

            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Error processing PDF with Mistral OCR: {e}", exc_info=True)
            raise

    async def _extract_from_image(self, image_content: bytes) -> str:
        """Extract text from image file using Mistral OCR API."""
        try:
            logger.info(f"Starting image processing with Mistral OCR, size: {len(image_content)} bytes")

            # Determine image format
            image = Image.open(io.BytesIO(image_content))
            image_format = image.format.lower() if image.format else 'png'

            # Save image to temporary file for Mistral OCR API
            with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{image_format}') as temp_file:
                temp_file.write(image_content)
                temp_file_path = temp_file.name

            try:
                # Use Mistral OCR API directly
                extracted_text = await self._extract_with_mistral_ocr_api(temp_file_path, "image")
                logger.info(f"Mistral OCR extracted text length: {len(extracted_text) if extracted_text else 0}")

                return f"## Extracted Text\n\n{extracted_text}" if extracted_text.strip() else "No text found in image"

            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Error processing image with Mistral OCR: {e}", exc_info=True)
            raise

    async def _extract_with_mistral_ocr_api(self, file_path: str, file_type: str) -> str:
        """
        Extract text from file using Mistral OCR API.

        Args:
            file_path: Path to the temporary file
            file_type: Type of file ("pdf" or "image")

        Returns:
            Extracted text in markdown format
        """
        try:
            logger.info(f"Starting Mistral OCR API for {file_type}: {file_path}")

            # Convert file to base64 for document_url
            with open(file_path, 'rb') as f:
                file_content = f.read()

            file_base64 = base64.b64encode(file_content).decode('utf-8')

            # Determine MIME type
            if file_type == "pdf":
                mime_type = "application/pdf"
            else:
                mime_type = "image/png"  # Default to PNG for images

            # Create data URL
            document_url = f"data:{mime_type};base64,{file_base64}"

            logger.info(f"Created document URL, size: {len(document_url)} characters")

            # Use Mistral OCR API with correct document type
            if file_type == "pdf":
                document_config = {
                    "type": "document_url",
                    "document_url": document_url
                }
            else:
                document_config = {
                    "type": "image_url",
                    "image_url": document_url
                }

            ocr_response = self.client.ocr.process(
                model="mistral-ocr-latest",
                document=document_config,
                include_image_base64=True
            )

            logger.info("Mistral OCR API call successful")

            # Extract text from response pages
            extracted_text = ""
            if hasattr(ocr_response, 'pages') and ocr_response.pages:
                all_pages_text = []
                for page in ocr_response.pages:
                    if hasattr(page, 'markdown') and page.markdown:
                        all_pages_text.append(page.markdown)
                extracted_text = "\n\n".join(all_pages_text)

            logger.info(f"Extracted text from {len(ocr_response.pages) if hasattr(ocr_response, 'pages') else 0} pages")

            logger.info(f"Mistral OCR completed, extracted text length: {len(extracted_text) if extracted_text else 0}")

            if extracted_text:
                logger.info(f"First 200 chars of extracted text: {extracted_text[:200]}...")

            return extracted_text.strip() if extracted_text else ""

        except Exception as e:
            logger.error(f"Error with Mistral OCR API: {e}", exc_info=True)
            return ""


# Global OCR service instance
ocr_service = OCRService()
