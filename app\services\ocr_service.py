import io
import logging
from typing import Union, List
from PIL import Image
import PyPDF2
from pdf2image import convert_from_bytes
from doctr.io import DocumentFile
from doctr.models import ocr_predictor


logger = logging.getLogger(__name__)


class OCRService:
    def __init__(self):
        """Initialize the OCR service with doc<PERSON> predictor."""
        try:
            # Initialize docTR OCR predictor
            self.predictor = ocr_predictor(pretrained=True)
            logger.info("OCR service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize OCR service: {e}")
            raise

    async def extract_text_from_file(self, file_content: bytes, filename: str) -> str:
        """
        Extract text from PDF or image file and return as markdown.
        
        Args:
            file_content: The file content as bytes
            filename: The original filename
            
        Returns:
            Extracted text formatted as markdown
        """
        try:
            file_extension = filename.lower().split('.')[-1]
            
            if file_extension == 'pdf':
                return await self._extract_from_pdf(file_content)
            elif file_extension in ['png', 'jpg', 'jpeg', 'tiff', 'bmp']:
                return await self._extract_from_image(file_content)
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")
                
        except Exception as e:
            logger.error(f"Error extracting text from {filename}: {e}")
            raise

    async def _extract_from_pdf(self, pdf_content: bytes) -> str:
        """Extract text from PDF file."""
        try:
            # Convert PDF pages to images
            images = convert_from_bytes(pdf_content)
            
            all_text = []
            for page_num, image in enumerate(images, 1):
                # Convert PIL image to format docTR can handle
                img_byte_arr = io.BytesIO()
                image.save(img_byte_arr, format='PNG')
                img_byte_arr.seek(0)
                
                # Use docTR to extract text
                doc = DocumentFile.from_images([image])
                result = self.predictor(doc)
                
                # Extract text from docTR result
                page_text = self._extract_text_from_doctr_result(result)
                
                if page_text.strip():
                    all_text.append(f"## Page {page_num}\n\n{page_text}")
            
            return "\n\n".join(all_text) if all_text else "No text found in PDF"
            
        except Exception as e:
            logger.error(f"Error processing PDF: {e}")
            raise

    async def _extract_from_image(self, image_content: bytes) -> str:
        """Extract text from image file."""
        try:
            # Load image
            image = Image.open(io.BytesIO(image_content))
            
            # Use docTR to extract text
            doc = DocumentFile.from_images([image])
            result = self.predictor(doc)
            
            # Extract text from docTR result
            text = self._extract_text_from_doctr_result(result)
            
            return f"## Extracted Text\n\n{text}" if text.strip() else "No text found in image"
            
        except Exception as e:
            logger.error(f"Error processing image: {e}")
            raise

    def _extract_text_from_doctr_result(self, result) -> str:
        """
        Extract text from docTR result and format it properly.
        
        Args:
            result: docTR prediction result
            
        Returns:
            Formatted text string
        """
        try:
            text_blocks = []
            
            # Navigate through docTR result structure
            for page in result.pages:
                page_blocks = []
                for block in page.blocks:
                    block_lines = []
                    for line in block.lines:
                        line_words = []
                        for word in line.words:
                            if hasattr(word, 'value') and word.value.strip():
                                line_words.append(word.value)
                        if line_words:
                            block_lines.append(' '.join(line_words))
                    if block_lines:
                        page_blocks.append('\n'.join(block_lines))
                
                if page_blocks:
                    text_blocks.extend(page_blocks)
            
            return '\n\n'.join(text_blocks)
            
        except Exception as e:
            logger.error(f"Error extracting text from docTR result: {e}")
            return ""


# Global OCR service instance
ocr_service = OCRService()
