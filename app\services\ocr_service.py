import io
import base64
import logging
from typing import Union, List
from PIL import Image
import PyPDF2
from pdf2image import convert_from_bytes
from mistralai import Mistral, UserMessage
from app.config import settings


logger = logging.getLogger(__name__)


class OCRService:
    def __init__(self):
        """Initialize the OCR service with Mistral client."""
        try:
            # Initialize Mistral client for OCR
            self.client = Mistral(api_key=settings.mistral_api_key)
            logger.info("Mistral OCR service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Mistral OCR service: {e}")
            raise

    async def extract_text_from_file(self, file_content: bytes, filename: str) -> str:
        """
        Extract text from PDF or image file and return as markdown using Mistral OCR.

        Args:
            file_content: The file content as bytes
            filename: The original filename

        Returns:
            Extracted text formatted as markdown
        """
        try:
            file_extension = filename.lower().split('.')[-1]

            if file_extension == 'pdf':
                return await self._extract_from_pdf(file_content)
            elif file_extension in ['png', 'jpg', 'jpeg', 'tiff', 'bmp']:
                return await self._extract_from_image(file_content)
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")

        except Exception as e:
            logger.error(f"Error extracting text from {filename}: {e}")
            raise

    async def _extract_from_pdf(self, pdf_content: bytes) -> str:
        """Extract text from PDF file using Mistral OCR."""
        try:
            # Convert PDF pages to images
            images = convert_from_bytes(pdf_content)

            all_text = []
            for page_num, image in enumerate(images, 1):
                # Extract text from this page using Mistral OCR
                page_text = await self._extract_text_with_mistral_ocr(image)

                if page_text.strip():
                    all_text.append(f"## Page {page_num}\n\n{page_text}")

            return "\n\n".join(all_text) if all_text else "No text found in PDF"

        except Exception as e:
            logger.error(f"Error processing PDF: {e}")
            raise

    async def _extract_from_image(self, image_content: bytes) -> str:
        """Extract text from image file using Mistral OCR."""
        try:
            # Load image
            image = Image.open(io.BytesIO(image_content))

            # Extract text using Mistral OCR
            text = await self._extract_text_with_mistral_ocr(image)

            return f"## Extracted Text\n\n{text}" if text.strip() else "No text found in image"

        except Exception as e:
            logger.error(f"Error processing image: {e}")
            raise

    async def _extract_text_with_mistral_ocr(self, image: Image.Image) -> str:
        """
        Extract text from image using Mistral OCR and return as markdown.

        Args:
            image: PIL Image object

        Returns:
            Extracted text in markdown format
        """
        try:
            # Convert image to base64
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr.seek(0)
            img_base64 = base64.b64encode(img_byte_arr.getvalue()).decode('utf-8')

            # Create the OCR prompt
            prompt = """Extract all text from this image and format it as clean, structured markdown.

Instructions:
1. Preserve the document structure and hierarchy
2. Use appropriate markdown formatting (headers, lists, tables, etc.)
3. Maintain the logical flow and organization of the content
4. For forms or structured documents, preserve the key-value relationships
5. Clean up any OCR artifacts or formatting issues
6. If there are tables, format them as markdown tables
7. Use headers (##, ###) to organize sections appropriately

Return only the markdown-formatted text content, no additional commentary."""

            # Call Mistral API with vision capabilities
            messages = [
                UserMessage(
                    content=[
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": f"data:image/png;base64,{img_base64}"
                        }
                    ]
                )
            ]

            response = self.client.chat.complete(
                model="pixtral-12b-2409",  # Mistral's vision model for OCR
                messages=messages,
                temperature=0.1,  # Low temperature for consistent OCR
                max_tokens=4000
            )

            extracted_text = response.choices[0].message.content
            return extracted_text.strip()

        except Exception as e:
            logger.error(f"Error with Mistral OCR: {e}")
            return ""


# Global OCR service instance
ocr_service = OCRService()
