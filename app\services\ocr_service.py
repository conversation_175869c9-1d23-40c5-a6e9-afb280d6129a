import io
import base64
import logging
from typing import Union, List
from PIL import Image
import PyPDF2
from pdf2image import convert_from_bytes
from mistralai import Mistral, UserMessage
from app.config import settings


logger = logging.getLogger(__name__)


class OCRService:
    def __init__(self):
        """Initialize the OCR service with Mistral client."""
        try:
            # Initialize Mistral client for OCR
            self.client = Mistral(api_key=settings.mistral_api_key)
            logger.info("Mistral OCR service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Mistral OCR service: {e}")
            raise

    async def extract_text_from_file(self, file_content: bytes, filename: str) -> str:
        """
        Extract text from PDF or image file and return as markdown using Mistral OCR.

        Args:
            file_content: The file content as bytes
            filename: The original filename

        Returns:
            Extracted text formatted as markdown
        """
        try:
            file_extension = filename.lower().split('.')[-1]

            if file_extension == 'pdf':
                return await self._extract_from_pdf(file_content)
            elif file_extension in ['png', 'jpg', 'jpeg', 'tiff', 'bmp']:
                return await self._extract_from_image(file_content)
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")

        except Exception as e:
            logger.error(f"Error extracting text from {filename}: {e}")
            raise

    async def _extract_from_pdf(self, pdf_content: bytes) -> str:
        """Extract text from PDF file using Mistral OCR."""
        try:
            logger.info(f"Starting PDF processing, size: {len(pdf_content)} bytes")

            # Convert PDF pages to images
            images = convert_from_bytes(pdf_content)
            logger.info(f"Successfully converted PDF to {len(images)} images")

            all_text = []
            for page_num, image in enumerate(images, 1):
                logger.info(f"Processing page {page_num}, image size: {image.size}")

                # Extract text from this page using Mistral OCR
                page_text = await self._extract_text_with_mistral_ocr(image)
                logger.info(f"Page {page_num} extracted text length: {len(page_text) if page_text else 0}")

                if page_text and page_text.strip():
                    all_text.append(f"## Page {page_num}\n\n{page_text}")
                else:
                    logger.warning(f"No text extracted from page {page_num}")

            result = "\n\n".join(all_text) if all_text else "No text found in PDF"
            logger.info(f"Final result length: {len(result)}")
            return result

        except Exception as e:
            logger.error(f"Error processing PDF: {e}", exc_info=True)
            raise

    async def _extract_from_image(self, image_content: bytes) -> str:
        """Extract text from image file using Mistral OCR."""
        try:
            # Load image
            image = Image.open(io.BytesIO(image_content))

            # Extract text using Mistral OCR
            text = await self._extract_text_with_mistral_ocr(image)

            return f"## Extracted Text\n\n{text}" if text.strip() else "No text found in image"

        except Exception as e:
            logger.error(f"Error processing image: {e}")
            raise

    async def _extract_text_with_mistral_ocr(self, image: Image.Image) -> str:
        """
        Extract text from image using Mistral OCR and return as markdown.

        Args:
            image: PIL Image object

        Returns:
            Extracted text in markdown format
        """
        try:
            logger.info(f"Starting Mistral OCR for image size: {image.size}")

            # Convert image to base64
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr.seek(0)
            img_base64 = base64.b64encode(img_byte_arr.getvalue()).decode('utf-8')
            logger.info(f"Image converted to base64, size: {len(img_base64)} characters")

            # Create the OCR prompt optimized for structured data extraction
            prompt = """Extract all text from this image and format it as clean, structured markdown optimized for data extraction.

Instructions:
1. Preserve the EXACT document structure and hierarchy
2. For FORMS and STRUCTURED DOCUMENTS:
   - Identify all form fields and their values
   - Preserve key-value relationships clearly
   - Use consistent formatting for field labels and values
3. For TABLES:
   - Format as proper markdown tables with headers
   - Preserve all rows and columns exactly
   - Include totals, subtotals, and calculated fields
   - Maintain numerical alignment and formatting
4. For PAYSTUBS/EARNINGS STATEMENTS:
   - Clearly separate employee info, pay details, earnings, and deductions
   - Preserve all numerical values with proper formatting ($, %, etc.)
   - Include both current period and year-to-date columns
5. For INVOICES/BILLS:
   - Separate header info, line items, and totals
   - Preserve all amounts, dates, and reference numbers
6. For TRANSCRIPTS:
   - Extract student information, school details, courses, grades, GPA
   - Preserve academic terms, credit hours, grade points
   - Include graduation information and degree details
7. Use appropriate markdown formatting:
   - Headers (##, ###) for major sections
   - Tables for tabular data
   - Lists for itemized information
   - Bold for field labels when appropriate
8. Clean up OCR artifacts but preserve all meaningful content
9. Maintain spatial relationships and groupings from the original document

Return only the markdown-formatted text content with maximum structural detail."""

            # Call Mistral API with vision capabilities
            messages = [
                UserMessage(
                    content=[
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": f"data:image/png;base64,{img_base64}"
                        }
                    ]
                )
            ]

            logger.info("Calling Mistral API for OCR...")

            # List of models to try
            models_to_try = [
                "pixtral-12b-2409",
                "pixtral-12b",
                "mistral-large-latest",
                "open-mistral-7b"
            ]

            response = None
            last_error = None

            for model_name in models_to_try:
                try:
                    logger.info(f"Trying model: {model_name}")
                    response = self.client.chat.complete(
                        model=model_name,
                        messages=messages,
                        temperature=0.1,
                        max_tokens=4000
                    )
                    logger.info(f"Model {model_name} successful")
                    break
                except Exception as model_error:
                    logger.warning(f"Model {model_name} failed: {model_error}")
                    last_error = model_error
                    continue

            if response is None:
                logger.error(f"All models failed. Last error: {last_error}")
                raise last_error

            extracted_text = response.choices[0].message.content
            logger.info(f"Mistral OCR completed, extracted text length: {len(extracted_text) if extracted_text else 0}")

            if extracted_text:
                logger.info(f"First 200 chars of extracted text: {extracted_text[:200]}...")

            return extracted_text.strip() if extracted_text else ""

        except Exception as e:
            logger.error(f"Error with Mistral OCR: {e}", exc_info=True)
            return ""


# Global OCR service instance
ocr_service = OCRService()
