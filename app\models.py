from typing import List, Dict, Optional
from pydantic import BaseModel


class KeyValuePair(BaseModel):
    """Model for a single key-value pair."""
    key: str
    value: str


class OCRResponse(BaseModel):
    """Response model for OCR extraction."""
    success: bool
    extracted_text: str
    message: Optional[str] = None


class KeyValueExtractionResponse(BaseModel):
    """Response model for key-value extraction."""
    success: bool
    key_value_pairs: List[Dict[str, str]]
    extracted_text: Optional[str] = None
    message: Optional[str] = None


class ProcessDocumentResponse(BaseModel):
    """Response model for complete document processing."""
    success: bool
    key_value_pairs: List[Dict[str, str]]
    extracted_text: str
    filename: str
    document_type: Optional[str] = None
    message: Optional[str] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    success: bool = False
    error: str
    message: str
