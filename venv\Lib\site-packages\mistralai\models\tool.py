"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .function import Function, FunctionTypedDict
from mistralai.types import BaseModel
import pydantic
from typing import Final, Optional, TypedDict
from typing_extensions import Annotated


class ToolTypedDict(TypedDict):
    function: FunctionTypedDict
    

class Tool(BaseModel):
    function: Function
    TYPE: Annotated[Final[Optional[str]], pydantic.Field(alias="type")] = "function" # type: ignore
    
