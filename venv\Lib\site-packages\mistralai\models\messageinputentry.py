"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .messageinputcontentchunks import (
    MessageInputContentChunks,
    MessageInputContentChunksTypedDict,
)
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import List, Literal, Optional, Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict


Object = Literal["entry"]

MessageInputEntryType = Literal["message.input"]

MessageInputEntryRole = Literal["assistant", "user"]

MessageInputEntryContentTypedDict = TypeAliasType(
    "MessageInputEntryContentTypedDict",
    Union[str, List[MessageInputContentChunksTypedDict]],
)


MessageInputEntryContent = TypeAliasType(
    "MessageInputEntryContent", Union[str, List[MessageInputContentChunks]]
)


class MessageInputEntryTypedDict(TypedDict):
    r"""Representation of an input message inside the conversation."""

    role: MessageInputEntryRole
    content: MessageInputEntryContentTypedDict
    object: NotRequired[Object]
    type: NotRequired[MessageInputEntryType]
    created_at: NotRequired[datetime]
    completed_at: NotRequired[Nullable[datetime]]
    id: NotRequired[str]
    prefix: NotRequired[bool]


class MessageInputEntry(BaseModel):
    r"""Representation of an input message inside the conversation."""

    role: MessageInputEntryRole

    content: MessageInputEntryContent

    object: Optional[Object] = "entry"

    type: Optional[MessageInputEntryType] = "message.input"

    created_at: Optional[datetime] = None

    completed_at: OptionalNullable[datetime] = UNSET

    id: Optional[str] = None

    prefix: Optional[bool] = False

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "object",
            "type",
            "created_at",
            "completed_at",
            "id",
            "prefix",
        ]
        nullable_fields = ["completed_at"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
