"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .entitytype import EntityType
from .shareenum import ShareEnum
from mistralai.types import BaseModel
from mistralai.utils import validate_open_enum
from pydantic.functional_validators import PlainValidator
from typing_extensions import Annotated, TypedDict


class SharingInTypedDict(TypedDict):
    org_id: str
    level: ShareEnum
    share_with_uuid: str
    r"""The id of the entity (user, workspace or organization) to share with"""
    share_with_type: EntityType
    r"""The type of entity, used to share a library."""


class SharingIn(BaseModel):
    org_id: str

    level: Annotated[ShareEnum, PlainValidator(validate_open_enum(False))]

    share_with_uuid: str
    r"""The id of the entity (user, workspace or organization) to share with"""

    share_with_type: Annotated[EntityType, PlainValidator(validate_open_enum(False))]
    r"""The type of entity, used to share a library."""
