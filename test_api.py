#!/usr/bin/env python3
"""
Test script for the OCR and Key-Value Extraction API.
"""

import requests
import json
import os
from pathlib import Path


def test_health_check():
    """Test the health check endpoint."""
    print("Testing health check...")
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the server is running.")
        return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    return True


def test_root_endpoint():
    """Test the root endpoint."""
    print("\nTesting root endpoint...")
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            print("✅ Root endpoint passed")
            data = response.json()
            print(f"API: {data['message']}")
            print(f"Version: {data['version']}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")


def test_extract_key_values_from_text():
    """Test key-value extraction from text."""
    print("\nTesting key-value extraction from text...")
    
    sample_text = """
    Driver's License
    
    Full Name: John Doe
    License Number: D123456789
    Date of Birth: 01/15/1990
    Address: 123 Main Street, Anytown, ST 12345
    Expiration Date: 01/15/2025
    Class: C
    """
    
    try:
        response = requests.post(
            "http://localhost:8000/extract-key-values",
            data={
                "text": sample_text,
                "document_type": "license"
            }
        )
        
        if response.status_code == 200:
            print("✅ Key-value extraction from text passed")
            data = response.json()
            print(f"Success: {data['success']}")
            print(f"Key-value pairs found: {len(data['key_value_pairs'])}")
            for pair in data['key_value_pairs']:
                print(f"  {pair}")
        else:
            print(f"❌ Key-value extraction failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Key-value extraction error: {e}")


def test_file_upload_with_sample():
    """Test file upload with a sample text file (simulating document)."""
    print("\nTesting file upload...")
    
    # Create a sample text file to test with
    sample_content = """
    INVOICE
    
    Invoice Number: INV-2024-001
    Date: March 15, 2024
    Due Date: April 15, 2024
    
    Bill To:
    John Smith
    123 Business Ave
    City, State 12345
    
    From:
    ABC Company
    456 Corporate Blvd
    Business City, ST 67890
    
    Description: Web Development Services
    Amount: $2,500.00
    Tax: $200.00
    Total: $2,700.00
    """
    
    # Save sample content as a temporary file
    temp_file = "temp_invoice.txt"
    with open(temp_file, "w") as f:
        f.write(sample_content)
    
    try:
        with open(temp_file, "rb") as f:
            response = requests.post(
                "http://localhost:8000/extract-text",
                files={"file": (temp_file, f, "text/plain")}
            )
        
        if response.status_code == 200:
            print("✅ File upload test passed")
            data = response.json()
            print(f"Success: {data['success']}")
            print(f"Extracted text length: {len(data['extracted_text'])}")
        else:
            print(f"❌ File upload failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ File upload error: {e}")
    finally:
        # Clean up temp file
        if os.path.exists(temp_file):
            os.remove(temp_file)


def main():
    """Run all tests."""
    print("🧪 Starting API Tests")
    print("=" * 50)
    
    # Test basic connectivity
    if not test_health_check():
        print("\n❌ Cannot connect to API. Please ensure:")
        print("1. The server is running (python run.py)")
        print("2. The server is accessible at http://localhost:8000")
        return
    
    # Test other endpoints
    test_root_endpoint()
    test_extract_key_values_from_text()
    test_file_upload_with_sample()
    
    print("\n" + "=" * 50)
    print("🏁 Tests completed!")
    print("\nNote: For full testing with OCR and Mistral LLM:")
    print("1. Set up your Mistral API key in .env file")
    print("2. Install all dependencies: pip install -r requirements.txt")
    print("3. Test with actual PDF/image files")


if __name__ == "__main__":
    main()
