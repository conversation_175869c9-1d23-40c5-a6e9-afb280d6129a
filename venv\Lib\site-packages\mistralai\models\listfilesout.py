"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .fileschema import FileSchema, FileSchemaTypedDict
from mistralai.types import BaseModel
from typing import List
from typing_extensions import TypedDict


class ListFilesOutTypedDict(TypedDict):
    data: List[FileSchemaTypedDict]
    object: str
    total: int


class ListFilesOut(BaseModel):
    data: List[FileSchema]

    object: str

    total: int
