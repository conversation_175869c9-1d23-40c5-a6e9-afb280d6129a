"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .assistantmessage import AssistantMessage, AssistantMessageTypedDict
from .systemmessage import SystemMessage, SystemMessageTypedDict
from .toolmessage import ToolMessage, ToolMessageTypedDict
from .usermessage import UserMessage, UserMessageTypedDict
from mistralai.types import BaseModel
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag
from typing import List, Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


InstructRequestMessagesTypedDict = TypeAliasType(
    "InstructRequestMessagesTypedDict",
    Union[
        SystemMessageTypedDict,
        UserMessageTypedDict,
        AssistantMessageTypedDict,
        ToolMessageTypedDict,
    ],
)


InstructRequestMessages = Annotated[
    Union[
        Annotated[AssistantMessage, Tag("assistant")],
        Annotated[SystemMessage, Tag("system")],
        Annotated[ToolMessage, Tag("tool")],
        Annotated[UserMessage, Tag("user")],
    ],
    Discriminator(lambda m: get_discriminator(m, "role", "role")),
]


class InstructRequestTypedDict(TypedDict):
    messages: List[InstructRequestMessagesTypedDict]


class InstructRequest(BaseModel):
    messages: List[InstructRequestMessages]
