"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .filepurpose import FilePurpose
from .sampletype import SampleType
from .source import Source
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from mistralai.utils import FieldMetadata, QueryParamMetadata, validate_open_enum
from pydantic import model_serializer
from pydantic.functional_validators import PlainValidator
from typing import List, Optional
from typing_extensions import Annotated, NotRequired, TypedDict


class FilesAPIRoutesListFilesRequestTypedDict(TypedDict):
    page: NotRequired[int]
    page_size: NotRequired[int]
    sample_type: NotRequired[Nullable[List[SampleType]]]
    source: NotRequired[Nullable[List[Source]]]
    search: NotRequired[Nullable[str]]
    purpose: NotRequired[Nullable[FilePurpose]]


class FilesAPIRoutesListFilesRequest(BaseModel):
    page: Annotated[
        Optional[int],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = 0

    page_size: Annotated[
        Optional[int],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = 100

    sample_type: Annotated[
        OptionalNullable[
            List[Annotated[SampleType, PlainValidator(validate_open_enum(False))]]
        ],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    source: Annotated[
        OptionalNullable[
            List[Annotated[Source, PlainValidator(validate_open_enum(False))]]
        ],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    search: Annotated[
        OptionalNullable[str],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    purpose: Annotated[
        Annotated[
            OptionalNullable[FilePurpose], PlainValidator(validate_open_enum(False))
        ],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "page",
            "page_size",
            "sample_type",
            "source",
            "search",
            "purpose",
        ]
        nullable_fields = ["sample_type", "source", "search", "purpose"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
