#!/usr/bin/env python3
"""
Test the application's OCR endpoint
"""

import requests
from PIL import Image, ImageDraw, ImageFont
import io

def create_test_image():
    """Create a simple test image with text."""
    # Create a white image
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Add some text
    try:
        # Try to use a default font
        font = ImageFont.load_default()
    except:
        font = None
    
    text = """Student Name: <PERSON>
Student ID: 123456789
GPA: 3.85
Major: Computer Science
Graduation Date: May 2024"""
    
    draw.text((20, 20), text, fill='black', font=font)
    
    # Save to bytes
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    
    return img_byte_arr.getvalue()

def test_app_debug_ocr():
    """Test the application's debug OCR endpoint."""
    print("Creating test image...")
    image_data = create_test_image()
    
    print("Testing application debug OCR endpoint...")
    try:
        response = requests.post(
            'http://localhost:8000/debug-ocr',
            files={'file': ('test_image.png', image_data, 'image/png')}
        )
        
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Application OCR test successful!")
            print(f"Success: {result['success']}")
            print(f"Message: {result['message']}")
            print(f"Extracted text: {result['extracted_text']}")
        else:
            print(f"❌ Application OCR test failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing application OCR: {e}")

def test_app_analyze_document():
    """Test the application's analyze-document endpoint."""
    print("\nTesting application analyze-document endpoint...")
    image_data = create_test_image()
    
    try:
        response = requests.post(
            'http://localhost:8000/analyze-document',
            files={'file': ('test_image.png', image_data, 'image/png')},
            data={'document_type': 'transcript'}
        )
        
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Application analyze-document test successful!")
            print(f"Success: {result['success']}")
            print(f"Message: {result['message']}")
            print(f"Key-value pairs: {len(result.get('key_value_pairs', []))}")
            if result.get('key_value_pairs'):
                print("First few key-value pairs:")
                for kv in result['key_value_pairs'][:3]:
                    print(f"  {kv}")
        else:
            print(f"❌ Application analyze-document test failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing application analyze-document: {e}")

if __name__ == "__main__":
    test_app_debug_ocr()
    test_app_analyze_document()
