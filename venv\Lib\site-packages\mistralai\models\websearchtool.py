"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


WebSearchToolType = Literal["web_search"]


class WebSearchToolTypedDict(TypedDict):
    type: NotRequired[WebSearchToolType]


class WebSearchTool(BaseModel):
    type: Optional[WebSearchToolType] = "web_search"
