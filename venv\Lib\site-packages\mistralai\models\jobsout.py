"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .jobout import JobOut, JobOutTypedDict
from mistralai.types import BaseModel
import pydantic
from typing import Final, List, Optional, TypedDict
from typing_extensions import Annotated, NotRequired


class JobsOutTypedDict(TypedDict):
    total: int
    data: NotRequired[List[JobOutTypedDict]]
    

class JobsOut(BaseModel):
    total: int
    data: Optional[List[JobOut]] = None
    OBJECT: Annotated[Final[Optional[str]], pydantic.Field(alias="object")] = "list" # type: ignore
    
