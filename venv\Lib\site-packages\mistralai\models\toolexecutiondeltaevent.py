"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .builtinconnectors import BuiltInConnectors
from datetime import datetime
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


ToolExecutionDeltaEventType = Literal["tool.execution.delta"]


class ToolExecutionDeltaEventTypedDict(TypedDict):
    id: str
    name: BuiltInConnectors
    arguments: str
    type: NotRequired[ToolExecutionDeltaEventType]
    created_at: NotRequired[datetime]
    output_index: NotRequired[int]


class ToolExecutionDeltaEvent(BaseModel):
    id: str

    name: BuiltInConnectors

    arguments: str

    type: Optional[ToolExecutionDeltaEventType] = "tool.execution.delta"

    created_at: Optional[datetime] = None

    output_index: Optional[int] = 0
