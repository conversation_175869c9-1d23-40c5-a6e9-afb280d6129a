"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .sharingout import SharingOut, SharingOutTypedDict
from mistralai.types import BaseModel
from typing import List
from typing_extensions import TypedDict


class ListSharingOutTypedDict(TypedDict):
    data: List[SharingOutTypedDict]


class ListSharingOut(BaseModel):
    data: List[SharingOut]
