import logging
import os
from typing import Optional
from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse

from app.config import settings
from app.models import (
    ProcessDocumentResponse,
    OCRResponse,
    KeyValueExtractionResponse,
    ErrorResponse,
    StructuredData
)
from app.services.ocr_service import ocr_service
from app.services.mistral_service import mistral_service


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="OCR and Key-Value Extraction API",
    description="Extract text from documents and convert to key-value pairs using OCR and Mistral LLM",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "OCR and Key-Value Extraction API",
        "version": "1.0.0",
        "endpoints": {
            "/process-document": "Upload and process document for key-value extraction",
            "/analyze-document": "AWS Textract-style document analysis with structured data",
            "/extract-text": "Extract text only from document",
            "/extract-key-values": "Extract key-value pairs from provided text",
            "/health": "Health check endpoint"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "message": "API is running"}


def validate_file(file: UploadFile) -> None:
    """Validate uploaded file."""
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Check file extension
    file_extension = file.filename.lower().split('.')[-1]
    allowed_extensions = settings.get_allowed_extensions()
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"File type '{file_extension}' not supported. Allowed types: {', '.join(allowed_extensions)}"
        )
    
    # Check file size (this is approximate since we're reading content)
    if hasattr(file, 'size') and file.size and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=400, 
            detail=f"File too large. Maximum size: {settings.max_file_size} bytes"
        )


@app.post("/analyze-document", response_model=ProcessDocumentResponse)
async def analyze_document_textract_style(
    file: UploadFile = File(...),
    document_type: Optional[str] = Form(default="document")
):
    """
    AWS Textract-style document analysis with comprehensive structured data extraction.

    Args:
        file: The document file (PDF or image)
        document_type: Type of document (paystub, invoice, license, transcript, etc.)

    Returns:
        ProcessDocumentResponse with comprehensive key-value pairs like AWS Textract
    """
    try:
        # Validate file
        validate_file(file)

        # Read file content
        file_content = await file.read()

        if len(file_content) > settings.max_file_size:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {settings.max_file_size} bytes"
            )

        logger.info(f"Analyzing document (Textract-style): {file.filename} (type: {document_type})")

        # Extract text using Mistral OCR with enhanced structure
        extracted_text = await ocr_service.extract_text_from_file(file_content, file.filename)

        if not extracted_text.strip():
            return ProcessDocumentResponse(
                success=False,
                key_value_pairs=[],
                extracted_text="",
                filename=file.filename,
                document_type=document_type,
                message="No text could be extracted from the document"
            )

        # Extract comprehensive key-value pairs using enhanced Mistral prompt
        key_value_pairs = await mistral_service.extract_key_value_pairs(extracted_text, document_type)

        return ProcessDocumentResponse(
            success=True,
            key_value_pairs=key_value_pairs,
            extracted_text=extracted_text,
            filename=file.filename,
            document_type=document_type,
            message=f"Successfully analyzed document and extracted {len(key_value_pairs)} key-value pairs (Textract-style)"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing document {file.filename}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/process-document", response_model=ProcessDocumentResponse)
async def process_document(
    file: UploadFile = File(...),
    document_type: Optional[str] = Form(default="document")
):
    """
    Complete document processing: OCR + Key-Value extraction.
    
    Args:
        file: The document file (PDF or image)
        document_type: Type of document (transcript, license, bill, invoice, etc.)
    
    Returns:
        ProcessDocumentResponse with extracted key-value pairs
    """
    try:
        # Validate file
        validate_file(file)
        
        # Read file content
        file_content = await file.read()
        
        if len(file_content) > settings.max_file_size:
            raise HTTPException(
                status_code=400, 
                detail=f"File too large. Maximum size: {settings.max_file_size} bytes"
            )
        
        logger.info(f"Processing document: {file.filename} (type: {document_type})")
        
        # Extract text using OCR
        extracted_text = await ocr_service.extract_text_from_file(file_content, file.filename)
        
        if not extracted_text.strip():
            return ProcessDocumentResponse(
                success=False,
                key_value_pairs=[],
                extracted_text="",
                filename=file.filename,
                document_type=document_type,
                message="No text could be extracted from the document"
            )
        
        # Extract key-value pairs using Mistral
        key_value_pairs = await mistral_service.extract_key_value_pairs(extracted_text, document_type)
        
        return ProcessDocumentResponse(
            success=True,
            key_value_pairs=key_value_pairs,
            extracted_text=extracted_text,
            filename=file.filename,
            document_type=document_type,
            message=f"Successfully processed {len(key_value_pairs)} key-value pairs"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing document {file.filename}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/extract-text", response_model=OCRResponse)
async def extract_text_only(file: UploadFile = File(...)):
    """
    Extract text from document using OCR only.
    
    Args:
        file: The document file (PDF or image)
    
    Returns:
        OCRResponse with extracted text
    """
    try:
        # Validate file
        validate_file(file)
        
        # Read file content
        file_content = await file.read()
        
        if len(file_content) > settings.max_file_size:
            raise HTTPException(
                status_code=400, 
                detail=f"File too large. Maximum size: {settings.max_file_size} bytes"
            )
        
        logger.info(f"Extracting text from: {file.filename}")
        
        # Extract text using OCR
        extracted_text = await ocr_service.extract_text_from_file(file_content, file.filename)
        
        return OCRResponse(
            success=True,
            extracted_text=extracted_text,
            message="Text extracted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting text from {file.filename}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/extract-key-values", response_model=KeyValueExtractionResponse)
async def extract_key_values_from_text(
    text: str = Form(...),
    document_type: Optional[str] = Form(default="document")
):
    """
    Extract key-value pairs from provided text using Mistral LLM.
    
    Args:
        text: The text to process
        document_type: Type of document (transcript, license, bill, invoice, etc.)
    
    Returns:
        KeyValueExtractionResponse with extracted key-value pairs
    """
    try:
        if not text.strip():
            raise HTTPException(status_code=400, detail="No text provided")
        
        logger.info(f"Extracting key-value pairs from text (type: {document_type})")
        
        # Extract key-value pairs using Mistral
        key_value_pairs = await mistral_service.extract_key_value_pairs(text, document_type)
        
        return KeyValueExtractionResponse(
            success=True,
            key_value_pairs=key_value_pairs,
            extracted_text=text,
            message=f"Successfully extracted {len(key_value_pairs)} key-value pairs"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting key-value pairs: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            message="An unexpected error occurred"
        ).dict()
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
