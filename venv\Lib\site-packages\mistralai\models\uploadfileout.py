"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .sampletype import SampleType
from .source import Source
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
import pydantic
from pydantic import model_serializer
from typing import Final, TypedDict
from typing_extensions import Annotated, NotRequired


class UploadFileOutTypedDict(TypedDict):
    id: str
    r"""The unique identifier of the file."""
    object: str
    r"""The object type, which is always \"file\"."""
    bytes: int
    r"""The size of the file, in bytes."""
    created_at: int
    r"""The UNIX timestamp (in seconds) of the event."""
    filename: str
    r"""The name of the uploaded file."""
    sample_type: SampleType
    source: Source
    num_lines: NotRequired[Nullable[int]]
    

class UploadFileOut(BaseModel):
    id: str
    r"""The unique identifier of the file."""
    object: str
    r"""The object type, which is always \"file\"."""
    bytes: int
    r"""The size of the file, in bytes."""
    created_at: int
    r"""The UNIX timestamp (in seconds) of the event."""
    filename: str
    r"""The name of the uploaded file."""
    sample_type: SampleType
    source: Source
    PURPOSE: Annotated[Final[str], pydantic.Field(alias="purpose")] = "fine-tune" # type: ignore
    r"""The intended purpose of the uploaded file. Only accepts fine-tuning (`fine-tune`) for now."""
    num_lines: OptionalNullable[int] = UNSET
    
    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["num_lines"]
        nullable_fields = ["num_lines"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (self.__pydantic_fields_set__.intersection({n}) or k in null_default_fields) # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
        
