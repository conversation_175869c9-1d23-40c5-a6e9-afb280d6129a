"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .completionargs import CompletionArgs, CompletionArgsTypedDict
from .conversationinputs import ConversationInputs, ConversationInputsTypedDict
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


ConversationRestartStreamRequestHandoffExecution = Literal["client", "server"]


class ConversationRestartStreamRequestTypedDict(TypedDict):
    r"""Request to restart a new conversation from a given entry in the conversation."""

    inputs: ConversationInputsTypedDict
    from_entry_id: str
    stream: NotRequired[bool]
    store: NotRequired[bool]
    r"""Whether to store the results into our servers or not."""
    handoff_execution: NotRequired[ConversationRestartStreamRequestHandoffExecution]
    completion_args: NotRequired[CompletionArgsTypedDict]
    r"""White-listed arguments from the completion API"""


class ConversationRestartStreamRequest(BaseModel):
    r"""Request to restart a new conversation from a given entry in the conversation."""

    inputs: ConversationInputs

    from_entry_id: str

    stream: Optional[bool] = True

    store: Optional[bool] = True
    r"""Whether to store the results into our servers or not."""

    handoff_execution: Optional[ConversationRestartStreamRequestHandoffExecution] = (
        "server"
    )

    completion_args: Optional[CompletionArgs] = None
    r"""White-listed arguments from the completion API"""
