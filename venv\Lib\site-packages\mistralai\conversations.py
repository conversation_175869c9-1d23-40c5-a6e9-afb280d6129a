"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from .basesdk import BaseSDK
from mistralai import models, utils
from mistralai._hooks import HookContext
from mistralai.types import OptionalNullable, UNSET
from mistralai.utils import eventstreaming, get_security_from_env
from typing import Any, List, Mapping, Optional, Union

# region imports
import typing
from typing import AsyncGenerator
import logging
from collections import defaultdict

from mistralai.models import (
    ResponseStartedEvent,
    ConversationEventsData,
    InputEntries,
)
from mistralai.extra.run.result import (
    RunR<PERSON>ult,
    RunResultEvents,
    FunctionResultEvent,
    reconstitue_entries,
)
from mistralai.extra.run.utils import run_requirements

logger = logging.getLogger(__name__)

if typing.TYPE_CHECKING:
    from mistralai.extra.run.context import RunContext

# endregion imports


class Conversations(BaseSDK):
    r"""(beta) Conversations API"""

    # region sdk-class-body
    # Custom run code allowing client side execution of code

    @run_requirements
    async def run_async(
        self,
        run_ctx: "RunContext",
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        instructions: OptionalNullable[str] = UNSET,
        tools: OptionalNullable[
            Union[List[models.Tools], List[models.ToolsTypedDict]]
        ] = UNSET,
        completion_args: OptionalNullable[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = UNSET,
        name: OptionalNullable[str] = UNSET,
        description: OptionalNullable[str] = UNSET,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> RunResult:
        """Run a conversation with the given inputs and context.

        The execution of a run will only stop when no required local execution can be done."""
        from mistralai.beta import Beta
        from mistralai.extra.run.context import _validate_run
        from mistralai.extra.run.tools import get_function_calls

        req, run_result, input_entries = await _validate_run(
            beta_client=Beta(self.sdk_configuration),
            run_ctx=run_ctx,
            inputs=inputs,
            instructions=instructions,
            tools=tools,
            completion_args=completion_args,
        )

        while True:
            if run_ctx.conversation_id is None:
                res = await self.start_async(
                    inputs=input_entries,
                    http_headers=http_headers,
                    name=name,
                    description=description,
                    retries=retries,
                    server_url=server_url,
                    timeout_ms=timeout_ms,
                    **req,
                )
                run_result.conversation_id = res.conversation_id
                run_ctx.conversation_id = res.conversation_id
                logger.info(
                    f"Started Run with conversation with id {res.conversation_id}"
                )
            else:
                res = await self.append_async(
                    conversation_id=run_ctx.conversation_id,
                    inputs=input_entries,
                    retries=retries,
                    server_url=server_url,
                    timeout_ms=timeout_ms,
                )
            run_ctx.request_count += 1
            run_result.output_entries.extend(res.outputs)
            fcalls = get_function_calls(res.outputs)
            if not fcalls:
                logger.debug("No more function calls to execute")
                break
            else:
                fresults = await run_ctx.execute_function_calls(fcalls)
                run_result.output_entries.extend(fresults)
                input_entries = typing.cast(list[InputEntries], fresults)
        return run_result

    @run_requirements
    async def run_stream_async(
        self,
        run_ctx: "RunContext",
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        instructions: OptionalNullable[str] = UNSET,
        tools: OptionalNullable[
            Union[List[models.Tools], List[models.ToolsTypedDict]]
        ] = UNSET,
        completion_args: OptionalNullable[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = UNSET,
        name: OptionalNullable[str] = UNSET,
        description: OptionalNullable[str] = UNSET,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> AsyncGenerator[Union[RunResultEvents, RunResult], None]:
        """Similar to `run_async` but returns a generator which streams events.

        The last streamed object is the RunResult object which summarises what happened in the run."""
        from mistralai.beta import Beta
        from mistralai.extra.run.context import _validate_run
        from mistralai.extra.run.tools import get_function_calls

        req, run_result, input_entries = await _validate_run(
            beta_client=Beta(self.sdk_configuration),
            run_ctx=run_ctx,
            inputs=inputs,
            instructions=instructions,
            tools=tools,
            completion_args=completion_args,
        )

        async def run_generator() -> (
            AsyncGenerator[Union[RunResultEvents, RunResult], None]
        ):
            current_entries = input_entries
            while True:
                received_event_tracker: defaultdict[
                    int, list[ConversationEventsData]
                ] = defaultdict(list)
                if run_ctx.conversation_id is None:
                    res = await self.start_stream_async(
                        inputs=current_entries,
                        http_headers=http_headers,
                        name=name,
                        description=description,
                        retries=retries,
                        server_url=server_url,
                        timeout_ms=timeout_ms,
                        **req,
                    )
                else:
                    res = await self.append_stream_async(
                        conversation_id=run_ctx.conversation_id,
                        inputs=current_entries,
                        retries=retries,
                        server_url=server_url,
                        timeout_ms=timeout_ms,
                    )
                async for event in res:
                    if (
                        isinstance(event.data, ResponseStartedEvent)
                        and run_ctx.conversation_id is None
                    ):
                        run_result.conversation_id = event.data.conversation_id
                        run_ctx.conversation_id = event.data.conversation_id
                        logger.info(
                            f"Started Run with conversation with id {run_ctx.conversation_id}"
                        )
                    if (
                        output_index := getattr(event.data, "output_index", None)
                    ) is not None:
                        received_event_tracker[output_index].append(event.data)
                    yield typing.cast(RunResultEvents, event)
                run_ctx.request_count += 1
                outputs = reconstitue_entries(received_event_tracker)
                run_result.output_entries.extend(outputs)
                fcalls = get_function_calls(outputs)
                if not fcalls:
                    logger.debug("No more function calls to execute")
                    break
                else:
                    fresults = await run_ctx.execute_function_calls(fcalls)
                    run_result.output_entries.extend(fresults)
                    for fresult in fresults:
                        yield RunResultEvents(
                            event="function.result",
                            data=FunctionResultEvent(
                                type="function.result",
                                result=fresult.result,
                                tool_call_id=fresult.tool_call_id,
                            ),
                        )
                    current_entries = typing.cast(list[InputEntries], fresults)
            yield run_result

        return run_generator()

    # endregion sdk-class-body

    def start(
        self,
        *,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        stream: Optional[bool] = False,
        store: OptionalNullable[bool] = UNSET,
        handoff_execution: OptionalNullable[models.HandoffExecution] = UNSET,
        instructions: OptionalNullable[str] = UNSET,
        tools: OptionalNullable[
            Union[List[models.Tools], List[models.ToolsTypedDict]]
        ] = UNSET,
        completion_args: OptionalNullable[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = UNSET,
        name: OptionalNullable[str] = UNSET,
        description: OptionalNullable[str] = UNSET,
        agent_id: OptionalNullable[str] = UNSET,
        model: OptionalNullable[str] = UNSET,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationResponse:
        r"""Create a conversation and append entries to it.

        Create a new conversation, using a base model or an agent and append entries. Completion and tool executions are run and the response is appended to the conversation.Use the returned conversation_id to continue the conversation.

        :param inputs:
        :param stream:
        :param store:
        :param handoff_execution:
        :param instructions:
        :param tools:
        :param completion_args:
        :param name:
        :param description:
        :param agent_id:
        :param model:
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.ConversationRequest(
            inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
            stream=stream,
            store=store,
            handoff_execution=handoff_execution,
            instructions=instructions,
            tools=utils.get_pydantic_model(tools, OptionalNullable[List[models.Tools]]),
            completion_args=utils.get_pydantic_model(
                completion_args, OptionalNullable[models.CompletionArgs]
            ),
            name=name,
            description=description,
            agent_id=agent_id,
            model=model,
        )

        req = self._build_request(
            method="POST",
            path="/v1/conversations",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=False,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request, False, False, "json", models.ConversationRequest
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_start",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationResponse)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def start_async(
        self,
        *,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        stream: Optional[bool] = False,
        store: OptionalNullable[bool] = UNSET,
        handoff_execution: OptionalNullable[models.HandoffExecution] = UNSET,
        instructions: OptionalNullable[str] = UNSET,
        tools: OptionalNullable[
            Union[List[models.Tools], List[models.ToolsTypedDict]]
        ] = UNSET,
        completion_args: OptionalNullable[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = UNSET,
        name: OptionalNullable[str] = UNSET,
        description: OptionalNullable[str] = UNSET,
        agent_id: OptionalNullable[str] = UNSET,
        model: OptionalNullable[str] = UNSET,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationResponse:
        r"""Create a conversation and append entries to it.

        Create a new conversation, using a base model or an agent and append entries. Completion and tool executions are run and the response is appended to the conversation.Use the returned conversation_id to continue the conversation.

        :param inputs:
        :param stream:
        :param store:
        :param handoff_execution:
        :param instructions:
        :param tools:
        :param completion_args:
        :param name:
        :param description:
        :param agent_id:
        :param model:
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.ConversationRequest(
            inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
            stream=stream,
            store=store,
            handoff_execution=handoff_execution,
            instructions=instructions,
            tools=utils.get_pydantic_model(tools, OptionalNullable[List[models.Tools]]),
            completion_args=utils.get_pydantic_model(
                completion_args, OptionalNullable[models.CompletionArgs]
            ),
            name=name,
            description=description,
            agent_id=agent_id,
            model=model,
        )

        req = self._build_request_async(
            method="POST",
            path="/v1/conversations",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=False,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request, False, False, "json", models.ConversationRequest
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_start",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationResponse)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    def list(
        self,
        *,
        page: Optional[int] = 0,
        page_size: Optional[int] = 100,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> List[models.ResponseBody]:
        r"""List all created conversations.

        Retrieve a list of conversation entities sorted by creation time.

        :param page:
        :param page_size:
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsListRequest(
            page=page,
            page_size=page_size,
        )

        req = self._build_request(
            method="GET",
            path="/v1/conversations",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=False,
            request_has_path_params=False,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_list",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, List[models.ResponseBody])
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def list_async(
        self,
        *,
        page: Optional[int] = 0,
        page_size: Optional[int] = 100,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> List[models.ResponseBody]:
        r"""List all created conversations.

        Retrieve a list of conversation entities sorted by creation time.

        :param page:
        :param page_size:
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsListRequest(
            page=page,
            page_size=page_size,
        )

        req = self._build_request_async(
            method="GET",
            path="/v1/conversations",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=False,
            request_has_path_params=False,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_list",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, List[models.ResponseBody])
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    def get(
        self,
        *,
        conversation_id: str,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.AgentsAPIV1ConversationsGetResponseV1ConversationsGet:
        r"""Retrieve a conversation information.

        Given a conversation_id retrieve a conversation entity with its attributes.

        :param conversation_id: ID of the conversation from which we are fetching metadata.
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsGetRequest(
            conversation_id=conversation_id,
        )

        req = self._build_request(
            method="GET",
            path="/v1/conversations/{conversation_id}",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=False,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_get",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(
                http_res.text,
                models.AgentsAPIV1ConversationsGetResponseV1ConversationsGet,
            )
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def get_async(
        self,
        *,
        conversation_id: str,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.AgentsAPIV1ConversationsGetResponseV1ConversationsGet:
        r"""Retrieve a conversation information.

        Given a conversation_id retrieve a conversation entity with its attributes.

        :param conversation_id: ID of the conversation from which we are fetching metadata.
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsGetRequest(
            conversation_id=conversation_id,
        )

        req = self._build_request_async(
            method="GET",
            path="/v1/conversations/{conversation_id}",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=False,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_get",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(
                http_res.text,
                models.AgentsAPIV1ConversationsGetResponseV1ConversationsGet,
            )
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    def append(
        self,
        *,
        conversation_id: str,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        stream: Optional[bool] = False,
        store: Optional[bool] = True,
        handoff_execution: Optional[
            models.ConversationAppendRequestHandoffExecution
        ] = "server",
        completion_args: Optional[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = None,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationResponse:
        r"""Append new entries to an existing conversation.

        Run completion on the history of the conversation and the user entries. Return the new created entries.

        :param conversation_id: ID of the conversation to which we append entries.
        :param inputs:
        :param stream:
        :param store: Whether to store the results into our servers or not.
        :param handoff_execution:
        :param completion_args: White-listed arguments from the completion API
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsAppendRequest(
            conversation_id=conversation_id,
            conversation_append_request=models.ConversationAppendRequest(
                inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
                stream=stream,
                store=store,
                handoff_execution=handoff_execution,
                completion_args=utils.get_pydantic_model(
                    completion_args, Optional[models.CompletionArgs]
                ),
            ),
        )

        req = self._build_request(
            method="POST",
            path="/v1/conversations/{conversation_id}",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request.conversation_append_request,
                False,
                False,
                "json",
                models.ConversationAppendRequest,
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_append",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationResponse)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def append_async(
        self,
        *,
        conversation_id: str,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        stream: Optional[bool] = False,
        store: Optional[bool] = True,
        handoff_execution: Optional[
            models.ConversationAppendRequestHandoffExecution
        ] = "server",
        completion_args: Optional[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = None,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationResponse:
        r"""Append new entries to an existing conversation.

        Run completion on the history of the conversation and the user entries. Return the new created entries.

        :param conversation_id: ID of the conversation to which we append entries.
        :param inputs:
        :param stream:
        :param store: Whether to store the results into our servers or not.
        :param handoff_execution:
        :param completion_args: White-listed arguments from the completion API
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsAppendRequest(
            conversation_id=conversation_id,
            conversation_append_request=models.ConversationAppendRequest(
                inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
                stream=stream,
                store=store,
                handoff_execution=handoff_execution,
                completion_args=utils.get_pydantic_model(
                    completion_args, Optional[models.CompletionArgs]
                ),
            ),
        )

        req = self._build_request_async(
            method="POST",
            path="/v1/conversations/{conversation_id}",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request.conversation_append_request,
                False,
                False,
                "json",
                models.ConversationAppendRequest,
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_append",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationResponse)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    def get_history(
        self,
        *,
        conversation_id: str,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationHistory:
        r"""Retrieve all entries in a conversation.

        Given a conversation_id retrieve all the entries belonging to that conversation. The entries are sorted in the order they were appended, those can be messages, connectors or function_call.

        :param conversation_id: ID of the conversation from which we are fetching entries.
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsHistoryRequest(
            conversation_id=conversation_id,
        )

        req = self._build_request(
            method="GET",
            path="/v1/conversations/{conversation_id}/history",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=False,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_history",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationHistory)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def get_history_async(
        self,
        *,
        conversation_id: str,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationHistory:
        r"""Retrieve all entries in a conversation.

        Given a conversation_id retrieve all the entries belonging to that conversation. The entries are sorted in the order they were appended, those can be messages, connectors or function_call.

        :param conversation_id: ID of the conversation from which we are fetching entries.
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsHistoryRequest(
            conversation_id=conversation_id,
        )

        req = self._build_request_async(
            method="GET",
            path="/v1/conversations/{conversation_id}/history",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=False,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_history",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationHistory)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    def get_messages(
        self,
        *,
        conversation_id: str,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationMessages:
        r"""Retrieve all messages in a conversation.

        Given a conversation_id retrieve all the messages belonging to that conversation. This is similar to retrieving all entries except we filter the messages only.

        :param conversation_id: ID of the conversation from which we are fetching messages.
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsMessagesRequest(
            conversation_id=conversation_id,
        )

        req = self._build_request(
            method="GET",
            path="/v1/conversations/{conversation_id}/messages",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=False,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_messages",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationMessages)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def get_messages_async(
        self,
        *,
        conversation_id: str,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationMessages:
        r"""Retrieve all messages in a conversation.

        Given a conversation_id retrieve all the messages belonging to that conversation. This is similar to retrieving all entries except we filter the messages only.

        :param conversation_id: ID of the conversation from which we are fetching messages.
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsMessagesRequest(
            conversation_id=conversation_id,
        )

        req = self._build_request_async(
            method="GET",
            path="/v1/conversations/{conversation_id}/messages",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=False,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_messages",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationMessages)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    def restart(
        self,
        *,
        conversation_id: str,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        from_entry_id: str,
        stream: Optional[bool] = False,
        store: Optional[bool] = True,
        handoff_execution: Optional[
            models.ConversationRestartRequestHandoffExecution
        ] = "server",
        completion_args: Optional[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = None,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationResponse:
        r"""Restart a conversation starting from a given entry.

        Given a conversation_id and an id, recreate a conversation from this point and run completion. A new conversation is returned with the new entries returned.

        :param conversation_id: ID of the original conversation which is being restarted.
        :param inputs:
        :param from_entry_id:
        :param stream:
        :param store: Whether to store the results into our servers or not.
        :param handoff_execution:
        :param completion_args: White-listed arguments from the completion API
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsRestartRequest(
            conversation_id=conversation_id,
            conversation_restart_request=models.ConversationRestartRequest(
                inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
                stream=stream,
                store=store,
                handoff_execution=handoff_execution,
                completion_args=utils.get_pydantic_model(
                    completion_args, Optional[models.CompletionArgs]
                ),
                from_entry_id=from_entry_id,
            ),
        )

        req = self._build_request(
            method="POST",
            path="/v1/conversations/{conversation_id}/restart",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request.conversation_restart_request,
                False,
                False,
                "json",
                models.ConversationRestartRequest,
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_restart",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationResponse)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def restart_async(
        self,
        *,
        conversation_id: str,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        from_entry_id: str,
        stream: Optional[bool] = False,
        store: Optional[bool] = True,
        handoff_execution: Optional[
            models.ConversationRestartRequestHandoffExecution
        ] = "server",
        completion_args: Optional[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = None,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.ConversationResponse:
        r"""Restart a conversation starting from a given entry.

        Given a conversation_id and an id, recreate a conversation from this point and run completion. A new conversation is returned with the new entries returned.

        :param conversation_id: ID of the original conversation which is being restarted.
        :param inputs:
        :param from_entry_id:
        :param stream:
        :param store: Whether to store the results into our servers or not.
        :param handoff_execution:
        :param completion_args: White-listed arguments from the completion API
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsRestartRequest(
            conversation_id=conversation_id,
            conversation_restart_request=models.ConversationRestartRequest(
                inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
                stream=stream,
                store=store,
                handoff_execution=handoff_execution,
                completion_args=utils.get_pydantic_model(
                    completion_args, Optional[models.CompletionArgs]
                ),
                from_entry_id=from_entry_id,
            ),
        )

        req = self._build_request_async(
            method="POST",
            path="/v1/conversations/{conversation_id}/restart",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request.conversation_restart_request,
                False,
                False,
                "json",
                models.ConversationRestartRequest,
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_restart",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.ConversationResponse)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    def start_stream(
        self,
        *,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        stream: Optional[bool] = True,
        store: OptionalNullable[bool] = UNSET,
        handoff_execution: OptionalNullable[
            models.ConversationStreamRequestHandoffExecution
        ] = UNSET,
        instructions: OptionalNullable[str] = UNSET,
        tools: OptionalNullable[
            Union[
                List[models.ConversationStreamRequestTools],
                List[models.ConversationStreamRequestToolsTypedDict],
            ]
        ] = UNSET,
        completion_args: OptionalNullable[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = UNSET,
        name: OptionalNullable[str] = UNSET,
        description: OptionalNullable[str] = UNSET,
        agent_id: OptionalNullable[str] = UNSET,
        model: OptionalNullable[str] = UNSET,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> eventstreaming.EventStream[models.ConversationEvents]:
        r"""Create a conversation and append entries to it.

        Create a new conversation, using a base model or an agent and append entries. Completion and tool executions are run and the response is appended to the conversation.Use the returned conversation_id to continue the conversation.

        :param inputs:
        :param stream:
        :param store:
        :param handoff_execution:
        :param instructions:
        :param tools:
        :param completion_args:
        :param name:
        :param description:
        :param agent_id:
        :param model:
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.ConversationStreamRequest(
            inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
            stream=stream,
            store=store,
            handoff_execution=handoff_execution,
            instructions=instructions,
            tools=utils.get_pydantic_model(
                tools, OptionalNullable[List[models.ConversationStreamRequestTools]]
            ),
            completion_args=utils.get_pydantic_model(
                completion_args, OptionalNullable[models.CompletionArgs]
            ),
            name=name,
            description=description,
            agent_id=agent_id,
            model=model,
        )

        req = self._build_request(
            method="POST",
            path="/v1/conversations#stream",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=False,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="text/event-stream",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request, False, False, "json", models.ConversationStreamRequest
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_start_stream",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            stream=True,
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "text/event-stream"):
            return eventstreaming.EventStream(
                http_res,
                lambda raw: utils.unmarshal_json(raw, models.ConversationEvents),
            )
        if utils.match_response(http_res, "422", "application/json"):
            http_res_text = utils.stream_to_text(http_res)
            response_data = utils.unmarshal_json(
                http_res_text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def start_stream_async(
        self,
        *,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        stream: Optional[bool] = True,
        store: OptionalNullable[bool] = UNSET,
        handoff_execution: OptionalNullable[
            models.ConversationStreamRequestHandoffExecution
        ] = UNSET,
        instructions: OptionalNullable[str] = UNSET,
        tools: OptionalNullable[
            Union[
                List[models.ConversationStreamRequestTools],
                List[models.ConversationStreamRequestToolsTypedDict],
            ]
        ] = UNSET,
        completion_args: OptionalNullable[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = UNSET,
        name: OptionalNullable[str] = UNSET,
        description: OptionalNullable[str] = UNSET,
        agent_id: OptionalNullable[str] = UNSET,
        model: OptionalNullable[str] = UNSET,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> eventstreaming.EventStreamAsync[models.ConversationEvents]:
        r"""Create a conversation and append entries to it.

        Create a new conversation, using a base model or an agent and append entries. Completion and tool executions are run and the response is appended to the conversation.Use the returned conversation_id to continue the conversation.

        :param inputs:
        :param stream:
        :param store:
        :param handoff_execution:
        :param instructions:
        :param tools:
        :param completion_args:
        :param name:
        :param description:
        :param agent_id:
        :param model:
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.ConversationStreamRequest(
            inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
            stream=stream,
            store=store,
            handoff_execution=handoff_execution,
            instructions=instructions,
            tools=utils.get_pydantic_model(
                tools, OptionalNullable[List[models.ConversationStreamRequestTools]]
            ),
            completion_args=utils.get_pydantic_model(
                completion_args, OptionalNullable[models.CompletionArgs]
            ),
            name=name,
            description=description,
            agent_id=agent_id,
            model=model,
        )

        req = self._build_request_async(
            method="POST",
            path="/v1/conversations#stream",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=False,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="text/event-stream",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request, False, False, "json", models.ConversationStreamRequest
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_start_stream",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            stream=True,
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "text/event-stream"):
            return eventstreaming.EventStreamAsync(
                http_res,
                lambda raw: utils.unmarshal_json(raw, models.ConversationEvents),
            )
        if utils.match_response(http_res, "422", "application/json"):
            http_res_text = await utils.stream_to_text_async(http_res)
            response_data = utils.unmarshal_json(
                http_res_text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    def append_stream(
        self,
        *,
        conversation_id: str,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        stream: Optional[bool] = True,
        store: Optional[bool] = True,
        handoff_execution: Optional[
            models.ConversationAppendStreamRequestHandoffExecution
        ] = "server",
        completion_args: Optional[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = None,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> eventstreaming.EventStream[models.ConversationEvents]:
        r"""Append new entries to an existing conversation.

        Run completion on the history of the conversation and the user entries. Return the new created entries.

        :param conversation_id: ID of the conversation to which we append entries.
        :param inputs:
        :param stream:
        :param store: Whether to store the results into our servers or not.
        :param handoff_execution:
        :param completion_args: White-listed arguments from the completion API
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsAppendStreamRequest(
            conversation_id=conversation_id,
            conversation_append_stream_request=models.ConversationAppendStreamRequest(
                inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
                stream=stream,
                store=store,
                handoff_execution=handoff_execution,
                completion_args=utils.get_pydantic_model(
                    completion_args, Optional[models.CompletionArgs]
                ),
            ),
        )

        req = self._build_request(
            method="POST",
            path="/v1/conversations/{conversation_id}#stream",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="text/event-stream",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request.conversation_append_stream_request,
                False,
                False,
                "json",
                models.ConversationAppendStreamRequest,
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_append_stream",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            stream=True,
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "text/event-stream"):
            return eventstreaming.EventStream(
                http_res,
                lambda raw: utils.unmarshal_json(raw, models.ConversationEvents),
            )
        if utils.match_response(http_res, "422", "application/json"):
            http_res_text = utils.stream_to_text(http_res)
            response_data = utils.unmarshal_json(
                http_res_text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def append_stream_async(
        self,
        *,
        conversation_id: str,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        stream: Optional[bool] = True,
        store: Optional[bool] = True,
        handoff_execution: Optional[
            models.ConversationAppendStreamRequestHandoffExecution
        ] = "server",
        completion_args: Optional[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = None,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> eventstreaming.EventStreamAsync[models.ConversationEvents]:
        r"""Append new entries to an existing conversation.

        Run completion on the history of the conversation and the user entries. Return the new created entries.

        :param conversation_id: ID of the conversation to which we append entries.
        :param inputs:
        :param stream:
        :param store: Whether to store the results into our servers or not.
        :param handoff_execution:
        :param completion_args: White-listed arguments from the completion API
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsAppendStreamRequest(
            conversation_id=conversation_id,
            conversation_append_stream_request=models.ConversationAppendStreamRequest(
                inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
                stream=stream,
                store=store,
                handoff_execution=handoff_execution,
                completion_args=utils.get_pydantic_model(
                    completion_args, Optional[models.CompletionArgs]
                ),
            ),
        )

        req = self._build_request_async(
            method="POST",
            path="/v1/conversations/{conversation_id}#stream",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="text/event-stream",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request.conversation_append_stream_request,
                False,
                False,
                "json",
                models.ConversationAppendStreamRequest,
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_append_stream",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            stream=True,
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "text/event-stream"):
            return eventstreaming.EventStreamAsync(
                http_res,
                lambda raw: utils.unmarshal_json(raw, models.ConversationEvents),
            )
        if utils.match_response(http_res, "422", "application/json"):
            http_res_text = await utils.stream_to_text_async(http_res)
            response_data = utils.unmarshal_json(
                http_res_text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    def restart_stream(
        self,
        *,
        conversation_id: str,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        from_entry_id: str,
        stream: Optional[bool] = True,
        store: Optional[bool] = True,
        handoff_execution: Optional[
            models.ConversationRestartStreamRequestHandoffExecution
        ] = "server",
        completion_args: Optional[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = None,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> eventstreaming.EventStream[models.ConversationEvents]:
        r"""Restart a conversation starting from a given entry.

        Given a conversation_id and an id, recreate a conversation from this point and run completion. A new conversation is returned with the new entries returned.

        :param conversation_id: ID of the original conversation which is being restarted.
        :param inputs:
        :param from_entry_id:
        :param stream:
        :param store: Whether to store the results into our servers or not.
        :param handoff_execution:
        :param completion_args: White-listed arguments from the completion API
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsRestartStreamRequest(
            conversation_id=conversation_id,
            conversation_restart_stream_request=models.ConversationRestartStreamRequest(
                inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
                stream=stream,
                store=store,
                handoff_execution=handoff_execution,
                completion_args=utils.get_pydantic_model(
                    completion_args, Optional[models.CompletionArgs]
                ),
                from_entry_id=from_entry_id,
            ),
        )

        req = self._build_request(
            method="POST",
            path="/v1/conversations/{conversation_id}/restart#stream",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="text/event-stream",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request.conversation_restart_stream_request,
                False,
                False,
                "json",
                models.ConversationRestartStreamRequest,
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_restart_stream",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            stream=True,
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "text/event-stream"):
            return eventstreaming.EventStream(
                http_res,
                lambda raw: utils.unmarshal_json(raw, models.ConversationEvents),
            )
        if utils.match_response(http_res, "422", "application/json"):
            http_res_text = utils.stream_to_text(http_res)
            response_data = utils.unmarshal_json(
                http_res_text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def restart_stream_async(
        self,
        *,
        conversation_id: str,
        inputs: Union[models.ConversationInputs, models.ConversationInputsTypedDict],
        from_entry_id: str,
        stream: Optional[bool] = True,
        store: Optional[bool] = True,
        handoff_execution: Optional[
            models.ConversationRestartStreamRequestHandoffExecution
        ] = "server",
        completion_args: Optional[
            Union[models.CompletionArgs, models.CompletionArgsTypedDict]
        ] = None,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> eventstreaming.EventStreamAsync[models.ConversationEvents]:
        r"""Restart a conversation starting from a given entry.

        Given a conversation_id and an id, recreate a conversation from this point and run completion. A new conversation is returned with the new entries returned.

        :param conversation_id: ID of the original conversation which is being restarted.
        :param inputs:
        :param from_entry_id:
        :param stream:
        :param store: Whether to store the results into our servers or not.
        :param handoff_execution:
        :param completion_args: White-listed arguments from the completion API
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.AgentsAPIV1ConversationsRestartStreamRequest(
            conversation_id=conversation_id,
            conversation_restart_stream_request=models.ConversationRestartStreamRequest(
                inputs=utils.get_pydantic_model(inputs, models.ConversationInputs),
                stream=stream,
                store=store,
                handoff_execution=handoff_execution,
                completion_args=utils.get_pydantic_model(
                    completion_args, Optional[models.CompletionArgs]
                ),
                from_entry_id=from_entry_id,
            ),
        )

        req = self._build_request_async(
            method="POST",
            path="/v1/conversations/{conversation_id}/restart#stream",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=True,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="text/event-stream",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request.conversation_restart_stream_request,
                False,
                False,
                "json",
                models.ConversationRestartStreamRequest,
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="agents_api_v1_conversations_restart_stream",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            stream=True,
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "text/event-stream"):
            return eventstreaming.EventStreamAsync(
                http_res,
                lambda raw: utils.unmarshal_json(raw, models.ConversationEvents),
            )
        if utils.match_response(http_res, "422", "application/json"):
            http_res_text = await utils.stream_to_text_async(http_res)
            response_data = utils.unmarshal_json(
                http_res_text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )
