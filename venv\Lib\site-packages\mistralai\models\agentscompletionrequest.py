"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .assistantmessage import AssistantMessage, AssistantMessageTypedDict
from .responseformat import ResponseFormat, ResponseFormatTypedDict
from .tool import Tool, ToolTypedDict
from .toolmessage import ToolMessage, ToolMessageTypedDict
from .usermessage import UserMessage, UserMessageTypedDict
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag, model_serializer
from typing import List, Literal, Optional, TypedDict, Union
from typing_extensions import Annotated, NotRequired


AgentsCompletionRequestStopTypedDict = Union[str, List[str]]
r"""Stop generation if this token is detected. Or if one of these tokens is detected when providing an array"""


AgentsCompletionRequestStop = Union[str, List[str]]
r"""Stop generation if this token is detected. Or if one of these tokens is detected when providing an array"""


AgentsCompletionRequestMessagesTypedDict = Union[UserMessageTypedDict, AssistantMessageTypedDict, ToolMessageTypedDict]


AgentsCompletionRequestMessages = Annotated[Union[Annotated[AssistantMessage, Tag("assistant")], Annotated[ToolMessage, Tag("tool")], Annotated[UserMessage, Tag("user")]], Discriminator(lambda m: get_discriminator(m, "role", "role"))]


AgentsCompletionRequestToolChoice = Literal["auto", "none", "any"]

class AgentsCompletionRequestTypedDict(TypedDict):
    messages: List[AgentsCompletionRequestMessagesTypedDict]
    r"""The prompt(s) to generate completions for, encoded as a list of dict with role and content."""
    agent_id: str
    r"""The ID of the agent to use for this completion."""
    max_tokens: NotRequired[Nullable[int]]
    r"""The maximum number of tokens to generate in the completion. The token count of your prompt plus `max_tokens` cannot exceed the model's context length."""
    min_tokens: NotRequired[Nullable[int]]
    r"""The minimum number of tokens to generate in the completion."""
    stream: NotRequired[bool]
    r"""Whether to stream back partial progress. If set, tokens will be sent as data-only server-side events as they become available, with the stream terminated by a data: [DONE] message. Otherwise, the server will hold the request open until the timeout or until completion, with the response containing the full result as JSON."""
    stop: NotRequired[AgentsCompletionRequestStopTypedDict]
    r"""Stop generation if this token is detected. Or if one of these tokens is detected when providing an array"""
    random_seed: NotRequired[Nullable[int]]
    r"""The seed to use for random sampling. If set, different calls will generate deterministic results."""
    response_format: NotRequired[ResponseFormatTypedDict]
    tools: NotRequired[Nullable[List[ToolTypedDict]]]
    tool_choice: NotRequired[AgentsCompletionRequestToolChoice]
    

class AgentsCompletionRequest(BaseModel):
    messages: List[AgentsCompletionRequestMessages]
    r"""The prompt(s) to generate completions for, encoded as a list of dict with role and content."""
    agent_id: str
    r"""The ID of the agent to use for this completion."""
    max_tokens: OptionalNullable[int] = UNSET
    r"""The maximum number of tokens to generate in the completion. The token count of your prompt plus `max_tokens` cannot exceed the model's context length."""
    min_tokens: OptionalNullable[int] = UNSET
    r"""The minimum number of tokens to generate in the completion."""
    stream: Optional[bool] = False
    r"""Whether to stream back partial progress. If set, tokens will be sent as data-only server-side events as they become available, with the stream terminated by a data: [DONE] message. Otherwise, the server will hold the request open until the timeout or until completion, with the response containing the full result as JSON."""
    stop: Optional[AgentsCompletionRequestStop] = None
    r"""Stop generation if this token is detected. Or if one of these tokens is detected when providing an array"""
    random_seed: OptionalNullable[int] = UNSET
    r"""The seed to use for random sampling. If set, different calls will generate deterministic results."""
    response_format: Optional[ResponseFormat] = None
    tools: OptionalNullable[List[Tool]] = UNSET
    tool_choice: Optional[AgentsCompletionRequestToolChoice] = "auto"
    
    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["max_tokens", "min_tokens", "stream", "stop", "random_seed", "response_format", "tools", "tool_choice"]
        nullable_fields = ["max_tokens", "min_tokens", "random_seed", "tools"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (self.__pydantic_fields_set__.intersection({n}) or k in null_default_fields) # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
        
