"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .functioncall import FunctionCall, FunctionCallTypedDict
from mistralai.types import BaseModel
import pydantic
from typing import Final, Optional, TypedDict
from typing_extensions import Annotated, NotRequired


class ToolCallTypedDict(TypedDict):
    function: FunctionCallTypedDict
    id: NotRequired[str]
    

class ToolCall(BaseModel):
    function: FunctionCall
    id: Optional[str] = "null"
    TYPE: Annotated[Final[Optional[str]], pydantic.Field(alias="type")] = "function" # type: ignore
    
