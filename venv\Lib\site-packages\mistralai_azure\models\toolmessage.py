"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai_azure.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import Literal, Optional, TypedDict
from typing_extensions import NotRequired


ToolMessageRole = Literal["tool"]

class ToolMessageTypedDict(TypedDict):
    content: str
    tool_call_id: NotRequired[Nullable[str]]
    name: NotRequired[Nullable[str]]
    role: NotRequired[ToolMessageRole]
    

class ToolMessage(BaseModel):
    content: str
    tool_call_id: OptionalNullable[str] = UNSET
    name: OptionalNullable[str] = UNSET
    role: Optional[ToolMessageRole] = "tool"
    
    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["tool_call_id", "name", "role"]
        nullable_fields = ["tool_call_id", "name"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (self.__pydantic_fields_set__.intersection({n}) or k in null_default_fields) # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
        
