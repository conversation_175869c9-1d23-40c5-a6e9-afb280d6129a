"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai_gcp.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


Type = Literal["text"]


class TextChunkTypedDict(TypedDict):
    text: str
    type: NotRequired[Type]


class TextChunk(BaseModel):
    text: str

    type: Optional[Type] = "text"
