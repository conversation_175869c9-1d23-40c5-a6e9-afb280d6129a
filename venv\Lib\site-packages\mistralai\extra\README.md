## Context

The extra package contains the custom logic which is too complex to be generated by Speakeasy from the OpenAPI specs. It was introduced to add the Structured Outputs feature.

## Development / Contributing

To add custom code in the SDK, you need to use [Speakeasy custom code regions](https://www.speakeasy.com/docs/customize/code/code-regions/overview) as below.

### Runbook of SDK customization

1. Add the code you want to import in the `src/mistralai/extra/` package. To have it importable from the SDK, you need to add it in the `__init__.py` file:
```python
from .my_custom_file import my_custom_function

__all__ = ["my_custom_function"]
```

2. Add a new custom code region in the SDK files, e.g in `src/mistralai/chat.py`:
```python
# region imports
from typing import Type
from mistralai.extra import my_custom_function
# endregion imports

class Chat(BaseSDK):
    r"""Chat Completion API."""

    # region sdk-class-body
    def my_custom_method(self, param: str) -> Type[some_type]:
        output = my_custom_function(param1)
        return output
    # endregion sdk-class-body
```

3. Now build the SDK with the custom code:
```bash
rm -rf dist; poetry build; python3 -m pip install ~/client-python/dist/mistralai-1.4.1-py3-none-any.whl --force-reinstall
```

4. And now you should be able to call the custom method:
```python
import os
from mistralai import Mistral

api_key = os.environ["MISTRAL_API_KEY"]
client = Mistral(api_key=api_key)

client.chat.my_custom_method(param="test")
```

### Run the unit tests

To run the unit tests for the `extra` package, you can run the following command from the root of the repository:
```bash
python3.12 -m unittest discover -s src/mistralai/extra/tests -t src
```
