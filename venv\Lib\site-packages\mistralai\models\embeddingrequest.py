"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
import pydantic
from pydantic import model_serializer
from typing import List, TypedDict, Union
from typing_extensions import Annotated, NotRequired


InputsTypedDict = Union[str, List[str]]
r"""Text to embed."""


Inputs = Union[str, List[str]]
r"""Text to embed."""


class EmbeddingRequestTypedDict(TypedDict):
    inputs: InputsTypedDict
    r"""Text to embed."""
    model: str
    r"""ID of the model to use."""
    encoding_format: NotRequired[Nullable[str]]
    r"""The format to return the embeddings in."""
    

class EmbeddingRequest(BaseModel):
    inputs: Annotated[Inputs, pydantic.Field(alias="input")]
    r"""Text to embed."""
    model: str
    r"""ID of the model to use."""
    encoding_format: OptionalNullable[str] = UNSET
    r"""The format to return the embeddings in."""
    
    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["encoding_format"]
        nullable_fields = ["encoding_format"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (self.__pydantic_fields_set__.intersection({n}) or k in null_default_fields) # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
        
