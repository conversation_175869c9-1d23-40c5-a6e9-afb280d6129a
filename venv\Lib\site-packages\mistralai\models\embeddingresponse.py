"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .embeddingresponsedata import EmbeddingResponseData, EmbeddingResponseDataTypedDict
from .usageinfo import UsageInfo, UsageInfoTypedDict
from mistralai.types import BaseModel
from typing import List
from typing_extensions import TypedDict


class EmbeddingResponseTypedDict(TypedDict):
    id: str
    object: str
    model: str
    usage: UsageInfoTypedDict
    data: List[EmbeddingResponseDataTypedDict]


class EmbeddingResponse(BaseModel):
    id: str

    object: str

    model: str

    usage: UsageInfo

    data: List[EmbeddingResponseData]
