"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .ocrimageobject import OCRImageObject, OCRImageObjectTypedDict
from .ocrpagedimensions import OCRPageDimensions, OCRPageDimensionsTypedDict
from mistralai.types import BaseModel, Nullable, UNSET_SENTINEL
from pydantic import model_serializer
from typing import List
from typing_extensions import TypedDict


class OCRPageObjectTypedDict(TypedDict):
    index: int
    r"""The page index in a pdf document starting from 0"""
    markdown: str
    r"""The markdown string response of the page"""
    images: List[OCRImageObjectTypedDict]
    r"""List of all extracted images in the page"""
    dimensions: Nullable[OCRPageDimensionsTypedDict]
    r"""The dimensions of the PDF Page's screenshot image"""


class OCRPageObject(BaseModel):
    index: int
    r"""The page index in a pdf document starting from 0"""

    markdown: str
    r"""The markdown string response of the page"""

    images: List[OCRImageObject]
    r"""List of all extracted images in the page"""

    dimensions: Nullable[OCRPageDimensions]
    r"""The dimensions of the PDF Page's screenshot image"""

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = []
        nullable_fields = ["dimensions"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
