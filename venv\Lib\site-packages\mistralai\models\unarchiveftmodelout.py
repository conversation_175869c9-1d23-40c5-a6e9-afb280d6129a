"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
import pydantic
from typing import Final, Optional, TypedDict
from typing_extensions import Annotated, NotRequired


class UnarchiveFTModelOutTypedDict(TypedDict):
    id: str
    archived: NotRequired[bool]
    

class UnarchiveFTModelOut(BaseModel):
    id: str
    OBJECT: Annotated[Final[Optional[str]], pydantic.Field(alias="object")] = "model" # type: ignore
    archived: Optional[bool] = False
    
