"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import TypedDict


class DeleteFileOutTypedDict(TypedDict):
    id: str
    r"""The ID of the deleted file."""
    object: str
    r"""The object type that was deleted"""
    deleted: bool
    r"""The deletion status."""
    

class DeleteFileOut(BaseModel):
    id: str
    r"""The ID of the deleted file."""
    object: str
    r"""The object type that was deleted"""
    deleted: bool
    r"""The deletion status."""
    
