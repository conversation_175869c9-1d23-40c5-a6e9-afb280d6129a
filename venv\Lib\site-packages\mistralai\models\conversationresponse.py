"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .agenthandoffentry import Agent<PERSON>andoffE<PERSON>ry, AgentHandoffEntryTypedDict
from .conversationusageinfo import ConversationUsageInfo, ConversationUsageInfoTypedDict
from .functioncallentry import FunctionCallEntry, FunctionCallEntryTypedDict
from .messageoutputentry import MessageOutputEntry, MessageOutputEntryTypedDict
from .toolexecutionentry import ToolExecutionEntry, ToolExecutionEntryTypedDict
from mistralai.types import BaseModel
from typing import List, Literal, Optional, Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict


ConversationResponseObject = Literal["conversation.response"]

OutputsTypedDict = TypeAliasType(
    "OutputsTypedDict",
    Union[
        ToolExecutionEntryTypedDict,
        FunctionCallEntryTypedDict,
        MessageOutputEntryTypedDict,
        AgentHandoffEntryTypedDict,
    ],
)


Outputs = TypeAliasType(
    "Outputs",
    Union[ToolExecutionEntry, FunctionCallEntry, MessageOutputEntry, AgentHandoffEntry],
)


class ConversationResponseTypedDict(TypedDict):
    r"""The response after appending new entries to the conversation."""

    conversation_id: str
    outputs: List[OutputsTypedDict]
    usage: ConversationUsageInfoTypedDict
    object: NotRequired[ConversationResponseObject]


class ConversationResponse(BaseModel):
    r"""The response after appending new entries to the conversation."""

    conversation_id: str

    outputs: List[Outputs]

    usage: ConversationUsageInfo

    object: Optional[ConversationResponseObject] = "conversation.response"
