"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .batcherror import Batch<PERSON>rror, BatchErrorTypedDict
from .batchjobstatus import BatchJobStatus
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import Any, Dict, List, Literal, Optional
from typing_extensions import NotRequired, TypedDict


BatchJobOutObject = Literal["batch"]


class BatchJobOutTypedDict(TypedDict):
    id: str
    input_files: List[str]
    endpoint: str
    model: str
    errors: List[BatchErrorTypedDict]
    status: BatchJobStatus
    created_at: int
    total_requests: int
    completed_requests: int
    succeeded_requests: int
    failed_requests: int
    object: NotRequired[BatchJobOutObject]
    metadata: NotRequired[Nullable[Dict[str, Any]]]
    output_file: NotRequired[Nullable[str]]
    error_file: NotRequired[Nullable[str]]
    started_at: NotRequired[Nullable[int]]
    completed_at: NotRequired[Nullable[int]]


class BatchJobOut(BaseModel):
    id: str

    input_files: List[str]

    endpoint: str

    model: str

    errors: List[BatchError]

    status: BatchJobStatus

    created_at: int

    total_requests: int

    completed_requests: int

    succeeded_requests: int

    failed_requests: int

    object: Optional[BatchJobOutObject] = "batch"

    metadata: OptionalNullable[Dict[str, Any]] = UNSET

    output_file: OptionalNullable[str] = UNSET

    error_file: OptionalNullable[str] = UNSET

    started_at: OptionalNullable[int] = UNSET

    completed_at: OptionalNullable[int] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "object",
            "metadata",
            "output_file",
            "error_file",
            "started_at",
            "completed_at",
        ]
        nullable_fields = [
            "metadata",
            "output_file",
            "error_file",
            "started_at",
            "completed_at",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
