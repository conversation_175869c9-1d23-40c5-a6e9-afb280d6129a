"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from .basesdk import BaseSDK
from mistralai import models, utils
from mistralai._hooks import HookContext
from mistralai.types import Nullable, OptionalNullable, UNSET
from mistralai.utils import get_security_from_env
from typing import Any, List, Mapping, Optional, Union


class Ocr(BaseSDK):
    r"""OCR API"""

    def process(
        self,
        *,
        model: Nullable[str],
        document: Union[models.Document, models.DocumentTypedDict],
        id: Optional[str] = None,
        pages: OptionalNullable[List[int]] = UNSET,
        include_image_base64: OptionalNullable[bool] = UNSET,
        image_limit: OptionalNullable[int] = UNSET,
        image_min_size: OptionalNullable[int] = UNSET,
        bbox_annotation_format: OptionalNullable[
            Union[models.ResponseFormat, models.ResponseFormatTypedDict]
        ] = UNSET,
        document_annotation_format: OptionalNullable[
            Union[models.ResponseFormat, models.ResponseFormatTypedDict]
        ] = UNSET,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.OCRResponse:
        r"""OCR

        :param model:
        :param document: Document to run OCR on
        :param id:
        :param pages: Specific pages user wants to process in various formats: single number, range, or list of both. Starts from 0
        :param include_image_base64: Include image URLs in response
        :param image_limit: Max images to extract
        :param image_min_size: Minimum height and width of image to extract
        :param bbox_annotation_format: Structured output class for extracting useful information from each extracted bounding box / image from document. Only json_schema is valid for this field
        :param document_annotation_format: Structured output class for extracting useful information from the entire document. Only json_schema is valid for this field
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.OCRRequest(
            model=model,
            id=id,
            document=utils.get_pydantic_model(document, models.Document),
            pages=pages,
            include_image_base64=include_image_base64,
            image_limit=image_limit,
            image_min_size=image_min_size,
            bbox_annotation_format=utils.get_pydantic_model(
                bbox_annotation_format, OptionalNullable[models.ResponseFormat]
            ),
            document_annotation_format=utils.get_pydantic_model(
                document_annotation_format, OptionalNullable[models.ResponseFormat]
            ),
        )

        req = self._build_request(
            method="POST",
            path="/v1/ocr",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=False,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request, False, False, "json", models.OCRRequest
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = self.do_request(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="ocr_v1_ocr_post",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.OCRResponse)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = utils.stream_to_text(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = utils.stream_to_text(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )

    async def process_async(
        self,
        *,
        model: Nullable[str],
        document: Union[models.Document, models.DocumentTypedDict],
        id: Optional[str] = None,
        pages: OptionalNullable[List[int]] = UNSET,
        include_image_base64: OptionalNullable[bool] = UNSET,
        image_limit: OptionalNullable[int] = UNSET,
        image_min_size: OptionalNullable[int] = UNSET,
        bbox_annotation_format: OptionalNullable[
            Union[models.ResponseFormat, models.ResponseFormatTypedDict]
        ] = UNSET,
        document_annotation_format: OptionalNullable[
            Union[models.ResponseFormat, models.ResponseFormatTypedDict]
        ] = UNSET,
        retries: OptionalNullable[utils.RetryConfig] = UNSET,
        server_url: Optional[str] = None,
        timeout_ms: Optional[int] = None,
        http_headers: Optional[Mapping[str, str]] = None,
    ) -> models.OCRResponse:
        r"""OCR

        :param model:
        :param document: Document to run OCR on
        :param id:
        :param pages: Specific pages user wants to process in various formats: single number, range, or list of both. Starts from 0
        :param include_image_base64: Include image URLs in response
        :param image_limit: Max images to extract
        :param image_min_size: Minimum height and width of image to extract
        :param bbox_annotation_format: Structured output class for extracting useful information from each extracted bounding box / image from document. Only json_schema is valid for this field
        :param document_annotation_format: Structured output class for extracting useful information from the entire document. Only json_schema is valid for this field
        :param retries: Override the default retry configuration for this method
        :param server_url: Override the default server URL for this method
        :param timeout_ms: Override the default request timeout configuration for this method in milliseconds
        :param http_headers: Additional headers to set or replace on requests.
        """
        base_url = None
        url_variables = None
        if timeout_ms is None:
            timeout_ms = self.sdk_configuration.timeout_ms

        if server_url is not None:
            base_url = server_url
        else:
            base_url = self._get_url(base_url, url_variables)

        request = models.OCRRequest(
            model=model,
            id=id,
            document=utils.get_pydantic_model(document, models.Document),
            pages=pages,
            include_image_base64=include_image_base64,
            image_limit=image_limit,
            image_min_size=image_min_size,
            bbox_annotation_format=utils.get_pydantic_model(
                bbox_annotation_format, OptionalNullable[models.ResponseFormat]
            ),
            document_annotation_format=utils.get_pydantic_model(
                document_annotation_format, OptionalNullable[models.ResponseFormat]
            ),
        )

        req = self._build_request_async(
            method="POST",
            path="/v1/ocr",
            base_url=base_url,
            url_variables=url_variables,
            request=request,
            request_body_required=True,
            request_has_path_params=False,
            request_has_query_params=True,
            user_agent_header="user-agent",
            accept_header_value="application/json",
            http_headers=http_headers,
            security=self.sdk_configuration.security,
            get_serialized_body=lambda: utils.serialize_request_body(
                request, False, False, "json", models.OCRRequest
            ),
            timeout_ms=timeout_ms,
        )

        if retries == UNSET:
            if self.sdk_configuration.retry_config is not UNSET:
                retries = self.sdk_configuration.retry_config

        retry_config = None
        if isinstance(retries, utils.RetryConfig):
            retry_config = (retries, ["429", "500", "502", "503", "504"])

        http_res = await self.do_request_async(
            hook_ctx=HookContext(
                config=self.sdk_configuration,
                base_url=base_url or "",
                operation_id="ocr_v1_ocr_post",
                oauth2_scopes=[],
                security_source=get_security_from_env(
                    self.sdk_configuration.security, models.Security
                ),
            ),
            request=req,
            error_status_codes=["422", "4XX", "5XX"],
            retry_config=retry_config,
        )

        response_data: Any = None
        if utils.match_response(http_res, "200", "application/json"):
            return utils.unmarshal_json(http_res.text, models.OCRResponse)
        if utils.match_response(http_res, "422", "application/json"):
            response_data = utils.unmarshal_json(
                http_res.text, models.HTTPValidationErrorData
            )
            raise models.HTTPValidationError(data=response_data)
        if utils.match_response(http_res, "4XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )
        if utils.match_response(http_res, "5XX", "*"):
            http_res_text = await utils.stream_to_text_async(http_res)
            raise models.SDKError(
                "API error occurred", http_res.status_code, http_res_text, http_res
            )

        content_type = http_res.headers.get("Content-Type")
        http_res_text = await utils.stream_to_text_async(http_res)
        raise models.SDKError(
            f"Unexpected response received (code: {http_res.status_code}, type: {content_type})",
            http_res.status_code,
            http_res_text,
            http_res,
        )
