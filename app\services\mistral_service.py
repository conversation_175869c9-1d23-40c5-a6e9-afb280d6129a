import json
import logging
from typing import List, Dict, Any
from mistralai import Mistral, UserMessage
from app.config import settings


logger = logging.getLogger(__name__)


class MistralService:
    def __init__(self):
        """Initialize the Mistral service."""
        try:
            self.client = Mistral(api_key=settings.mistral_api_key)
            logger.info("Mistral service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Mistral service: {e}")
            raise

    async def extract_key_value_pairs(self, extracted_text: str, document_type: str = "document") -> List[Dict[str, str]]:
        """
        Extract key-value pairs from the markdown-formatted text using Mistral LLM.

        Args:
            extracted_text: The markdown-formatted text extracted from OCR
            document_type: Type of document (transcript, license, bill, invoice, etc.)

        Returns:
            List of key-value pairs in format [{key: value}, {key: value}]
        """
        try:
            # Create a comprehensive prompt for key-value extraction from markdown
            prompt = self._create_extraction_prompt(extracted_text, document_type)

            # Call Mistral API
            messages = [
                UserMessage(content=prompt)
            ]

            response = self.client.chat.complete(
                model="mistral-large-latest",
                messages=messages,
                temperature=0.1,  # Low temperature for consistent extraction
                max_tokens=2000
            )

            # Parse the response
            result_text = response.choices[0].message.content
            key_value_pairs = self._parse_response(result_text)

            return key_value_pairs

        except Exception as e:
            logger.error(f"Error extracting key-value pairs: {e}")
            raise

    def _create_extraction_prompt(self, text: str, document_type: str) -> str:
        """Create a detailed prompt for key-value extraction from markdown text, similar to AWS Textract."""

        prompt = f"""
You are an expert document analysis AI that extracts structured data like AWS Textract. Extract key-value pairs from the following markdown-formatted {document_type} text with high accuracy.

INSTRUCTIONS - Extract data similar to AWS Textract format:
1. Analyze the markdown text carefully and identify ALL meaningful key-value relationships
2. Pay special attention to:
   - Form fields and their values
   - Table data (convert table rows to key-value pairs)
   - Headers and their associated content
   - Numerical data (amounts, dates, IDs, etc.)
   - Personal information fields

3. For PAYSTUB/EARNINGS documents, extract:
   - Employee details: Name, ID, Social Security Number, Address
   - Pay period information: Pay date, Period ending, Hours worked
   - Earnings: Regular pay, Overtime, Gross pay, Net pay
   - Deductions: Federal tax, State tax, Social Security, Medicare, etc.
   - Year-to-date totals for all categories

4. For INVOICES/BILLS, extract:
   - Invoice details: Invoice Number, Date, Due Date, PO Number
   - Vendor/Company information: Name, Address, Phone, Email
   - Customer/Bill-to information: Name, Address
   - Line items: Description, Quantity, Rate, Amount
   - Totals: Subtotal, Tax, Total Amount

5. For DRIVER'S LICENSE, extract:
   - Personal info: Full Name, Date of Birth, Address, Height, Weight, Eye Color
   - License info: License Number, Class, Expiration Date, Issue Date
   - Restrictions and endorsements

6. For any TABLES in the document:
   - Convert each row to key-value pairs
   - Use column headers as key prefixes
   - Include totals and subtotals

7. Return ONLY a valid JSON array in this exact format: [{{"key": "value"}}, {{"key": "value"}}]
8. Use descriptive keys that match AWS Textract style:
   - "Employee Name" not just "Name"
   - "Pay Period Ending" not just "Period"
   - "Federal Income Tax" not just "Federal"
   - "Gross Pay Current" and "Gross Pay YTD" for current and year-to-date
9. Clean up values: remove markdown formatting, extra spaces
10. For monetary amounts: preserve format like "$1,234.56"
11. For dates: use consistent format like "MM/DD/YYYY"
12. For percentages: include the % symbol
13. Extract BOTH current period and year-to-date values when available

MARKDOWN DOCUMENT TEXT:
{text}

RESPONSE (JSON array only - extract ALL meaningful data like AWS Textract):"""

        return prompt

    def _parse_response(self, response_text: str) -> List[Dict[str, str]]:
        """
        Parse the Mistral response and extract key-value pairs.
        
        Args:
            response_text: Raw response from Mistral
            
        Returns:
            List of key-value pairs
        """
        try:
            # Clean up the response text
            cleaned_text = response_text.strip()
            
            # Try to find JSON array in the response
            start_idx = cleaned_text.find('[')
            end_idx = cleaned_text.rfind(']') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = cleaned_text[start_idx:end_idx]
                
                # Parse JSON
                parsed_data = json.loads(json_str)
                
                # Validate format
                if isinstance(parsed_data, list):
                    validated_pairs = []
                    for item in parsed_data:
                        if isinstance(item, dict) and len(item) == 1:
                            # Each item should have exactly one key-value pair
                            key, value = next(iter(item.items()))
                            if key and value and isinstance(key, str) and isinstance(value, str):
                                validated_pairs.append({key.strip(): value.strip()})
                    
                    return validated_pairs
            
            # If JSON parsing fails, try to extract key-value pairs manually
            return self._fallback_extraction(cleaned_text)
            
        except json.JSONDecodeError as e:
            logger.warning(f"JSON parsing failed: {e}. Attempting fallback extraction.")
            return self._fallback_extraction(response_text)
        except Exception as e:
            logger.error(f"Error parsing Mistral response: {e}")
            return []

    def _fallback_extraction(self, text: str) -> List[Dict[str, str]]:
        """
        Fallback method to extract key-value pairs when JSON parsing fails.
        
        Args:
            text: Raw text to parse
            
        Returns:
            List of key-value pairs
        """
        try:
            pairs = []
            lines = text.split('\n')
            
            for line in lines:
                line = line.strip()
                
                # Look for common key-value patterns
                if ':' in line:
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        key = parts[0].strip().strip('"\'')
                        value = parts[1].strip().strip('"\'')
                        
                        if key and value:
                            pairs.append({key: value})
                
                # Look for patterns like "Key = Value" or "Key - Value"
                elif '=' in line or ' - ' in line:
                    separator = '=' if '=' in line else ' - '
                    parts = line.split(separator, 1)
                    if len(parts) == 2:
                        key = parts[0].strip().strip('"\'')
                        value = parts[1].strip().strip('"\'')
                        
                        if key and value:
                            pairs.append({key: value})
            
            return pairs[:20]  # Limit to 20 pairs to avoid overwhelming response
            
        except Exception as e:
            logger.error(f"Fallback extraction failed: {e}")
            return []


# Global Mistral service instance
mistral_service = MistralService()
