import json
import logging
from typing import List, Dict, Any
from mistralai.client import MistralClient
from mistralai.models.chat_completion import ChatMessage
from app.config import settings


logger = logging.getLogger(__name__)


class MistralService:
    def __init__(self):
        """Initialize the Mistral service."""
        try:
            self.client = MistralClient(api_key=settings.mistral_api_key)
            logger.info("Mistral service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Mistral service: {e}")
            raise

    async def extract_key_value_pairs(self, extracted_text: str, document_type: str = "document") -> List[Dict[str, str]]:
        """
        Extract key-value pairs from the extracted text using Mistral LLM.
        
        Args:
            extracted_text: The text extracted from OCR
            document_type: Type of document (transcript, license, bill, invoice, etc.)
            
        Returns:
            List of key-value pairs in format [{key: value}, {key: value}]
        """
        try:
            # Create a comprehensive prompt for key-value extraction
            prompt = self._create_extraction_prompt(extracted_text, document_type)
            
            # Call Mistral API
            messages = [
                ChatMessage(role="user", content=prompt)
            ]
            
            response = self.client.chat(
                model="mistral-large-latest",
                messages=messages,
                temperature=0.1,  # Low temperature for consistent extraction
                max_tokens=2000
            )
            
            # Parse the response
            result_text = response.choices[0].message.content
            key_value_pairs = self._parse_response(result_text)
            
            return key_value_pairs
            
        except Exception as e:
            logger.error(f"Error extracting key-value pairs: {e}")
            raise

    def _create_extraction_prompt(self, text: str, document_type: str) -> str:
        """Create a detailed prompt for key-value extraction."""
        
        prompt = f"""
You are an expert document analysis AI. Extract key-value pairs from the following {document_type} text with high accuracy.

INSTRUCTIONS:
1. Analyze the text carefully and identify all meaningful key-value relationships
2. For different document types, focus on relevant fields:
   - Driver's License: Name, License Number, DOB, Address, Expiration Date, etc.
   - Invoice/Bill: Invoice Number, Date, Amount, Vendor, Customer, Items, etc.
   - Transcript: Student Name, School, GPA, Courses, Grades, etc.
   - General Documents: Any structured information present

3. Return ONLY a valid JSON array in this exact format: [{{"key": "value"}}, {{"key": "value"}}]
4. Use clear, descriptive keys (e.g., "Full Name" instead of just "Name")
5. Clean up values (remove extra spaces, fix formatting)
6. If a value spans multiple lines, join them appropriately
7. Skip empty or meaningless values
8. For dates, standardize format when possible
9. For amounts/numbers, preserve original formatting but clean up

DOCUMENT TEXT:
{text}

RESPONSE (JSON array only):"""

        return prompt

    def _parse_response(self, response_text: str) -> List[Dict[str, str]]:
        """
        Parse the Mistral response and extract key-value pairs.
        
        Args:
            response_text: Raw response from Mistral
            
        Returns:
            List of key-value pairs
        """
        try:
            # Clean up the response text
            cleaned_text = response_text.strip()
            
            # Try to find JSON array in the response
            start_idx = cleaned_text.find('[')
            end_idx = cleaned_text.rfind(']') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = cleaned_text[start_idx:end_idx]
                
                # Parse JSON
                parsed_data = json.loads(json_str)
                
                # Validate format
                if isinstance(parsed_data, list):
                    validated_pairs = []
                    for item in parsed_data:
                        if isinstance(item, dict) and len(item) == 1:
                            # Each item should have exactly one key-value pair
                            key, value = next(iter(item.items()))
                            if key and value and isinstance(key, str) and isinstance(value, str):
                                validated_pairs.append({key.strip(): value.strip()})
                    
                    return validated_pairs
            
            # If JSON parsing fails, try to extract key-value pairs manually
            return self._fallback_extraction(cleaned_text)
            
        except json.JSONDecodeError as e:
            logger.warning(f"JSON parsing failed: {e}. Attempting fallback extraction.")
            return self._fallback_extraction(response_text)
        except Exception as e:
            logger.error(f"Error parsing Mistral response: {e}")
            return []

    def _fallback_extraction(self, text: str) -> List[Dict[str, str]]:
        """
        Fallback method to extract key-value pairs when JSON parsing fails.
        
        Args:
            text: Raw text to parse
            
        Returns:
            List of key-value pairs
        """
        try:
            pairs = []
            lines = text.split('\n')
            
            for line in lines:
                line = line.strip()
                
                # Look for common key-value patterns
                if ':' in line:
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        key = parts[0].strip().strip('"\'')
                        value = parts[1].strip().strip('"\'')
                        
                        if key and value:
                            pairs.append({key: value})
                
                # Look for patterns like "Key = Value" or "Key - Value"
                elif '=' in line or ' - ' in line:
                    separator = '=' if '=' in line else ' - '
                    parts = line.split(separator, 1)
                    if len(parts) == 2:
                        key = parts[0].strip().strip('"\'')
                        value = parts[1].strip().strip('"\'')
                        
                        if key and value:
                            pairs.append({key: value})
            
            return pairs[:20]  # Limit to 20 pairs to avoid overwhelming response
            
        except Exception as e:
            logger.error(f"Fallback extraction failed: {e}")
            return []


# Global Mistral service instance
mistral_service = MistralService()
