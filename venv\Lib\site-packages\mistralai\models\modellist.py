"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .modelcard import ModelCard, ModelCardTypedDict
from mistralai.types import BaseModel
from typing import List, Optional, TypedDict
from typing_extensions import NotRequired


class ModelListTypedDict(TypedDict):
    object: NotRequired[str]
    data: NotRequired[List[ModelCardTypedDict]]
    

class ModelList(BaseModel):
    object: Optional[str] = "list"
    data: Optional[List[ModelCard]] = None
    
