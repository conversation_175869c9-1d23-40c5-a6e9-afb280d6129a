"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .metricout import MetricOut, MetricOutTypedDict
from mistralai.types import BaseModel
from typing_extensions import TypedDict


class CheckpointOutTypedDict(TypedDict):
    metrics: MetricOutTypedDict
    r"""Metrics at the step number during the fine-tuning job. Use these metrics to assess if the training is going smoothly (loss should decrease, token accuracy should increase)."""
    step_number: int
    r"""The step number that the checkpoint was created at."""
    created_at: int
    r"""The UNIX timestamp (in seconds) for when the checkpoint was created."""


class CheckpointOut(BaseModel):
    metrics: MetricOut
    r"""Metrics at the step number during the fine-tuning job. Use these metrics to assess if the training is going smoothly (loss should decrease, token accuracy should increase)."""

    step_number: int
    r"""The step number that the checkpoint was created at."""

    created_at: int
    r"""The UNIX timestamp (in seconds) for when the checkpoint was created."""
