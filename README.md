# OCR and Key-Value Extraction API

A FastAPI application that extracts text from PDF and image files using docTR OCR, then processes the extracted text with Mistral LLM to extract key-value pairs similar to Amazon Textract.

## Features

- **OCR Text Extraction**: Uses docTR (by <PERSON><PERSON>) for high-accuracy text extraction from PDFs and images
- **Key-Value Extraction**: Leverages Mistral LLM to extract structured key-value pairs from extracted text
- **Multiple Document Types**: Supports transcripts, driver's licenses, bills, invoices, and general documents
- **RESTful API**: FastAPI-based API with automatic documentation
- **File Format Support**: PDF, PNG, JPG, JPEG, TIFF, BMP

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd orc
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your Mistral API key:
   ```
   MISTRAL_API_KEY=your_mistral_api_key_here
   MAX_FILE_SIZE=10485760
   ALLOWED_EXTENSIONS=pdf,png,jpg,jpeg,tiff,bmp
   ```

## Usage

1. **Start the server**:
   ```bash
   python run.py
   ```
   
   The API will be available at `http://localhost:8000`

2. **API Documentation**:
   - Swagger UI: `http://localhost:8000/docs`
   - ReDoc: `http://localhost:8000/redoc`

## API Endpoints

### POST `/process-document`
Complete document processing: OCR + Key-Value extraction

**Parameters**:
- `file`: Document file (PDF or image)
- `document_type`: Optional document type (transcript, license, bill, invoice, etc.)

**Response**:
```json
{
  "success": true,
  "key_value_pairs": [
    {"Full Name": "John Doe"},
    {"License Number": "D123456789"},
    {"Date of Birth": "01/01/1990"}
  ],
  "extracted_text": "...",
  "filename": "license.pdf",
  "document_type": "license",
  "message": "Successfully processed 3 key-value pairs"
}
```

### POST `/extract-text`
Extract text only using OCR

**Parameters**:
- `file`: Document file (PDF or image)

**Response**:
```json
{
  "success": true,
  "extracted_text": "## Page 1\n\nExtracted text content...",
  "message": "Text extracted successfully"
}
```

### POST `/extract-key-values`
Extract key-value pairs from provided text

**Parameters**:
- `text`: Text to process
- `document_type`: Optional document type

**Response**:
```json
{
  "success": true,
  "key_value_pairs": [
    {"key": "value"},
    {"key": "value"}
  ],
  "message": "Successfully extracted 2 key-value pairs"
}
```

## Example Usage

### Using curl

```bash
# Process a document
curl -X POST "http://localhost:8000/process-document" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "document_type=invoice"

# Extract text only
curl -X POST "http://localhost:8000/extract-text" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf"
```

### Using Python requests

```python
import requests

# Process document
with open('document.pdf', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/process-document',
        files={'file': f},
        data={'document_type': 'invoice'}
    )
    result = response.json()
    print(result['key_value_pairs'])
```

## Supported Document Types

The system is optimized for various document types:

- **Driver's License**: Extracts name, license number, DOB, address, expiration date
- **Invoice/Bill**: Extracts invoice number, date, amount, vendor, customer, items
- **Transcript**: Extracts student name, school, GPA, courses, grades
- **General Documents**: Extracts any structured information present

## Configuration

Environment variables in `.env`:

- `MISTRAL_API_KEY`: Your Mistral API key (required)
- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 10MB)
- `ALLOWED_EXTENSIONS`: Comma-separated list of allowed file extensions

## Dependencies

- **FastAPI**: Web framework
- **docTR**: OCR library by Mindee
- **Mistral AI**: LLM for key-value extraction
- **PyPDF2**: PDF processing
- **pdf2image**: PDF to image conversion
- **Pillow**: Image processing

## Error Handling

The API includes comprehensive error handling:

- File validation (size, type)
- OCR processing errors
- LLM API errors
- Malformed responses

## Performance Notes

- OCR processing time depends on document size and complexity
- PDF files are converted to images for processing
- Large files may take longer to process
- Consider implementing async processing for production use

## License

[Add your license information here]
