"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .inputentries import InputEntries, InputEntriesTypedDict
from typing import List, Union
from typing_extensions import TypeAliasType


ConversationInputsTypedDict = TypeAliasType(
    "ConversationInputsTypedDict", Union[str, List[InputEntriesTypedDict]]
)


ConversationInputs = TypeAliasType("ConversationInputs", Union[str, List[InputEntries]])
