"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .batchjobstatus import BatchJobStatus
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from mistralai.utils import FieldMetadata, QueryParamMetadata
from pydantic import model_serializer
from typing import Any, Dict, List, Optional
from typing_extensions import Annotated, NotRequired, TypedDict


class JobsAPIRoutesBatchGetBatchJobsRequestTypedDict(TypedDict):
    page: NotRequired[int]
    page_size: NotRequired[int]
    model: NotRequired[Nullable[str]]
    metadata: NotRequired[Nullable[Dict[str, Any]]]
    created_after: NotRequired[Nullable[datetime]]
    created_by_me: NotRequired[bool]
    status: NotRequired[Nullable[List[BatchJobStatus]]]


class JobsAPIRoutesBatchGetBatchJobsRequest(BaseModel):
    page: Annotated[
        Optional[int],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = 0

    page_size: Annotated[
        Optional[int],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = 100

    model: Annotated[
        OptionalNullable[str],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    metadata: Annotated[
        OptionalNullable[Dict[str, Any]],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    created_after: Annotated[
        OptionalNullable[datetime],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    created_by_me: Annotated[
        Optional[bool],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = False

    status: Annotated[
        OptionalNullable[List[BatchJobStatus]],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "page",
            "page_size",
            "model",
            "metadata",
            "created_after",
            "created_by_me",
            "status",
        ]
        nullable_fields = ["model", "metadata", "created_after", "status"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
