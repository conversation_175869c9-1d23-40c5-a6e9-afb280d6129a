"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import Dict, Optional
from typing_extensions import NotRequired, TypedDict


class ModerationObjectTypedDict(TypedDict):
    categories: NotRequired[Dict[str, bool]]
    r"""Moderation result thresholds"""
    category_scores: NotRequired[Dict[str, float]]
    r"""Moderation result"""


class ModerationObject(BaseModel):
    categories: Optional[Dict[str, bool]] = None
    r"""Moderation result thresholds"""

    category_scores: Optional[Dict[str, float]] = None
    r"""Moderation result"""
