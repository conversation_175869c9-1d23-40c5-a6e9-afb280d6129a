"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .batchjobout import Batch<PERSON>obOut, BatchJobOutTypedDict
from mistralai.types import BaseModel
from typing import List, Literal, Optional
from typing_extensions import NotRequired, TypedDict


BatchJobsOutObject = Literal["list"]


class BatchJobsOutTypedDict(TypedDict):
    total: int
    data: NotRequired[List[BatchJobOutTypedDict]]
    object: NotRequired[BatchJobsOutObject]


class BatchJobsOut(BaseModel):
    total: int

    data: Optional[List[BatchJobOut]] = None

    object: Optional[BatchJobsOutObject] = "list"
