"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .assistantmessage import AssistantM<PERSON><PERSON>, AssistantMessageTypedDict
from mistralai_gcp.types import BaseModel, UnrecognizedStr
from mistralai_gcp.utils import validate_open_enum
from pydantic.functional_validators import PlainValidator
from typing import Literal, Union
from typing_extensions import Annotated, TypedDict


ChatCompletionChoiceFinishReason = Union[
    Literal["stop", "length", "model_length", "error", "tool_calls"], UnrecognizedStr
]


class ChatCompletionChoiceTypedDict(TypedDict):
    index: int
    message: AssistantMessageTypedDict
    finish_reason: ChatCompletionChoiceFinishReason


class ChatCompletionChoice(BaseModel):
    index: int

    message: AssistantMessage

    finish_reason: Annotated[
        ChatCompletionChoiceFinishReason, PlainValidator(validate_open_enum(False))
    ]
