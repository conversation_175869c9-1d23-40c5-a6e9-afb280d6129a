"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, QueryParamMetadata
from typing_extensions import Annotated, TypedDict


class AgentsAPIV1AgentsUpdateVersionRequestTypedDict(TypedDict):
    agent_id: str
    version: int


class AgentsAPIV1AgentsUpdateVersionRequest(BaseModel):
    agent_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    version: Annotated[
        int, FieldMetadata(query=QueryParamMetadata(style="form", explode=True))
    ]
