#!/usr/bin/env python3
"""
Test PDF OCR with Mistral API directly
"""

import os
import base64
from mistralai import Mistra<PERSON>

def test_pdf_ocr():
    """Test PDF OCR with a sample PDF URL."""
    try:
        # Initialize Mistral client
        api_key = "vKLtdsfpBsXtL1tKvXBsILEEZk6O0Xty"
        client = Mistral(api_key=api_key)
        
        print("Testing PDF OCR with sample PDF...")
        
        # Use a sample PDF URL (ArXiv paper)
        pdf_url = "https://arxiv.org/pdf/2201.04234"
        
        # Call OCR API for PDF
        ocr_response = client.ocr.process(
            model="mistral-ocr-latest",
            document={
                "type": "document_url",
                "document_url": pdf_url
            },
            include_image_base64=True
        )
        
        print("✅ PDF OCR test successful!")
        print(f"Response type: {type(ocr_response)}")
        print(f"Number of pages: {len(ocr_response.pages) if hasattr(ocr_response, 'pages') else 0}")
        
        # Extract text from pages
        if hasattr(ocr_response, 'pages') and ocr_response.pages:
            for i, page in enumerate(ocr_response.pages[:2]):  # Show first 2 pages
                if hasattr(page, 'markdown') and page.markdown:
                    print(f"\n--- Page {i+1} (first 500 chars) ---")
                    print(page.markdown[:500])
                    print("...")
        
    except Exception as e:
        print(f"❌ Error testing PDF OCR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pdf_ocr()
