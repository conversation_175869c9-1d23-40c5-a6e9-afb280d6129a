"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .updateftmodelin import UpdateFTModelIn, UpdateFTModelInTypedDict
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing import TypedDict
from typing_extensions import Annotated


class JobsAPIRoutesFineTuningUpdateFineTunedModelRequestTypedDict(TypedDict):
    model_id: str
    r"""The ID of the model to update."""
    update_ft_model_in: UpdateFTModelInTypedDict
    

class JobsAPIRoutesFineTuningUpdateFineTunedModelRequest(BaseModel):
    model_id: Annotated[str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))]
    r"""The ID of the model to update."""
    update_ft_model_in: Annotated[UpdateFTModelIn, FieldMetadata(request=RequestMetadata(media_type="application/json"))]
    
