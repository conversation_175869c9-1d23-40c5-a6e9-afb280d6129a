#!/usr/bin/env python3
"""
Simple test to create a test image and test OCR
"""

from PIL import Image, ImageDraw, ImageFont
import requests
import io

def create_test_image():
    """Create a simple test image with text."""
    # Create a white image
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Add some text
    try:
        # Try to use a default font
        font = ImageFont.load_default()
    except:
        font = None
    
    text = """Student Name: <PERSON>
Student ID: 123456789
GPA: 3.85
Major: Computer Science
Graduation Date: May 2024"""
    
    draw.text((20, 20), text, fill='black', font=font)
    
    # Save to bytes
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    
    return img_byte_arr.getvalue()

def test_ocr():
    """Test the OCR endpoint with the test image."""
    print("Creating test image...")
    image_data = create_test_image()
    
    print("Testing debug OCR endpoint...")
    try:
        response = requests.post(
            'http://localhost:8000/debug-ocr',
            files={'file': ('test_image.png', image_data, 'image/png')}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ OCR test successful!")
            print(f"Success: {result['success']}")
            print(f"Message: {result['message']}")
            print(f"Extracted text: {result['extracted_text'][:200]}...")
        else:
            print(f"❌ OCR test failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error testing OCR: {e}")

if __name__ == "__main__":
    test_ocr()
