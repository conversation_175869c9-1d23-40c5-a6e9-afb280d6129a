"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .textchunk import TextChunk, TextChunkTypedDict
from mistralai_gcp.types import BaseModel
from typing import List, Literal, Optional, TypedDict, Union
from typing_extensions import NotRequired


UserMessageContentTypedDict = Union[str, List[TextChunkTypedDict]]


UserMessageContent = Union[str, List[TextChunk]]


UserMessageRole = Literal["user"]

class UserMessageTypedDict(TypedDict):
    content: UserMessageContentTypedDict
    role: NotRequired[UserMessageRole]
    

class UserMessage(BaseModel):
    content: UserMessageContent
    role: Optional[UserMessageRole] = "user"
    
